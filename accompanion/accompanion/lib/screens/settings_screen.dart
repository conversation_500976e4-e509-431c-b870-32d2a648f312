import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';

/// 应用设置数据模型
class AppSettings {
  final bool enableNotifications;
  final bool enableAutoCalculation;
  final bool enableEnergyTips;
  final bool enableDarkMode;
  final double defaultTemperature;
  final double temperatureTolerance;
  final String temperatureUnit;
  final String language;
  final bool enableHapticFeedback;
  final bool enableSounds;
  final double fontSize;
  final bool enableDataSync;
  final bool enableAnalytics;

  const AppSettings({
    this.enableNotifications = true,
    this.enableAutoCalculation = true,
    this.enableEnergyTips = true,
    this.enableDarkMode = false,
    this.defaultTemperature = 24.0,
    this.temperatureTolerance = 1.0,
    this.temperatureUnit = '°C',
    this.language = '中文',
    this.enableHapticFeedback = true,
    this.enableSounds = true,
    this.fontSize = 16.0,
    this.enableDataSync = false,
    this.enableAnalytics = true,
  });

  AppSettings copyWith({
    bool? enableNotifications,
    bool? enableAutoCalculation,
    bool? enableEnergyTips,
    bool? enableDarkMode,
    double? defaultTemperature,
    double? temperatureTolerance,
    String? temperatureUnit,
    String? language,
    bool? enableHapticFeedback,
    bool? enableSounds,
    double? fontSize,
    bool? enableDataSync,
    bool? enableAnalytics,
  }) {
    return AppSettings(
      enableNotifications: enableNotifications ?? this.enableNotifications,
      enableAutoCalculation:
          enableAutoCalculation ?? this.enableAutoCalculation,
      enableEnergyTips: enableEnergyTips ?? this.enableEnergyTips,
      enableDarkMode: enableDarkMode ?? this.enableDarkMode,
      defaultTemperature: defaultTemperature ?? this.defaultTemperature,
      temperatureTolerance: temperatureTolerance ?? this.temperatureTolerance,
      temperatureUnit: temperatureUnit ?? this.temperatureUnit,
      language: language ?? this.language,
      enableHapticFeedback: enableHapticFeedback ?? this.enableHapticFeedback,
      enableSounds: enableSounds ?? this.enableSounds,
      fontSize: fontSize ?? this.fontSize,
      enableDataSync: enableDataSync ?? this.enableDataSync,
      enableAnalytics: enableAnalytics ?? this.enableAnalytics,
    );
  }
}

/// 设置状态管理
class SettingsNotifier extends StateNotifier<AppSettings> {
  SettingsNotifier() : super(const AppSettings());

  /// 切换通知
  void toggleNotifications() {
    state = state.copyWith(enableNotifications: !state.enableNotifications);
    _saveSettings();
  }

  /// 切换自动计算
  void toggleAutoCalculation() {
    state = state.copyWith(enableAutoCalculation: !state.enableAutoCalculation);
    _saveSettings();
  }

  /// 切换节能提示
  void toggleEnergyTips() {
    state = state.copyWith(enableEnergyTips: !state.enableEnergyTips);
    _saveSettings();
  }

  /// 切换深色模式
  void toggleDarkMode() {
    state = state.copyWith(enableDarkMode: !state.enableDarkMode);
    _saveSettings();
  }

  /// 更新默认温度
  void updateDefaultTemperature(double temperature) {
    state = state.copyWith(defaultTemperature: temperature);
    _saveSettings();
  }

  /// 更新温度容差
  void updateTemperatureTolerance(double tolerance) {
    state = state.copyWith(temperatureTolerance: tolerance);
    _saveSettings();
  }

  /// 更新温度单位
  void updateTemperatureUnit(String unit) {
    state = state.copyWith(temperatureUnit: unit);
    _saveSettings();
  }

  /// 更新语言
  void updateLanguage(String language) {
    state = state.copyWith(language: language);
    _saveSettings();
  }

  /// 切换触觉反馈
  void toggleHapticFeedback() {
    state = state.copyWith(enableHapticFeedback: !state.enableHapticFeedback);
    _saveSettings();
  }

  /// 切换声音
  void toggleSounds() {
    state = state.copyWith(enableSounds: !state.enableSounds);
    _saveSettings();
  }

  /// 更新字体大小
  void updateFontSize(double fontSize) {
    state = state.copyWith(fontSize: fontSize);
    _saveSettings();
  }

  /// 切换数据同步
  void toggleDataSync() {
    state = state.copyWith(enableDataSync: !state.enableDataSync);
    _saveSettings();
  }

  /// 切换数据分析
  void toggleAnalytics() {
    state = state.copyWith(enableAnalytics: !state.enableAnalytics);
    _saveSettings();
  }

  /// 重置所有设置
  void resetAllSettings() {
    state = const AppSettings();
    _saveSettings();
  }

  /// 保存设置到本地存储
  void _saveSettings() async {
    try {
      final box = await Hive.openBox('app_settings');

      // 保存各种设置
      await box.put('enable_notifications', state.enableNotifications);
      await box.put('enable_auto_calculation', state.enableAutoCalculation);
      await box.put('enable_energy_tips', state.enableEnergyTips);
      await box.put('enable_dark_mode', state.enableDarkMode);
      await box.put('default_temperature', state.defaultTemperature);
      await box.put('temperature_tolerance', state.temperatureTolerance);
      await box.put('temperature_unit', state.temperatureUnit);
      await box.put('language', state.language);
      await box.put('enable_haptic_feedback', state.enableHapticFeedback);
      await box.put('enable_sounds', state.enableSounds);
      await box.put('font_size', state.fontSize);
      await box.put('enable_data_sync', state.enableDataSync);
      await box.put('enable_analytics', state.enableAnalytics);

      debugPrint('✅ 设置已保存到本地存储');
    } catch (e) {
      debugPrint('❌ 保存设置失败: $e');
    }
  }
}

/// 设置状态提供者
final settingsProvider = StateNotifierProvider<SettingsNotifier, AppSettings>((
  ref,
) {
  return SettingsNotifier();
});

/// 设置页面 - 应用配置管理
///
/// 功能特点：
/// - 用户偏好设置
/// - 通知和提醒配置
/// - 界面和交互设置
/// - 数据管理和隐私设置
class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settings = ref.watch(settingsProvider);

    return Scaffold(
      backgroundColor: const Color(0xFFFAFAFA),
      appBar: AppBar(
        title: const Text(
          '设置',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(color: Color(0xFF37352F)),
        actions: [
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: Color(0xFF6E6E6E)),
            onSelected: (value) {
              switch (value) {
                case 'reset':
                  _showResetDialog(context, ref);
                  break;
                case 'export':
                  _showExportDialog(context);
                  break;
                case 'import':
                  _showImportDialog(context);
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'reset', child: Text('重置设置')),
              const PopupMenuItem(value: 'export', child: Text('导出设置')),
              const PopupMenuItem(value: 'import', child: Text('导入设置')),
            ],
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // 用户信息区域
          _buildUserInfoSection(context),
          const SizedBox(height: 24),
          // 通知设置
          _buildNotificationSection(context, ref, settings),
          const SizedBox(height: 16),
          // 计算设置
          _buildCalculationSection(context, ref, settings),
          const SizedBox(height: 16),
          // 界面设置
          _buildInterfaceSection(context, ref, settings),
          const SizedBox(height: 16),
          // 数据和隐私
          _buildDataPrivacySection(context, ref, settings),
          const SizedBox(height: 16),
          // 关于应用
          _buildAboutSection(context),
        ],
      ),
    );
  }

  /// 构建用户信息区域
  Widget _buildUserInfoSection(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF2F76DA), Color(0xFF1E5BB8)],
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(30),
              ),
              child: const Icon(Icons.person, size: 32, color: Colors.white),
            ),
            const SizedBox(width: 16),
            const Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '空调管家用户',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    '智能优化，节能舒适',
                    style: TextStyle(fontSize: 14, color: Colors.white70),
                  ),
                ],
              ),
            ),
            IconButton(
              onPressed: () {
                // 显示用户信息编辑功能开发中的提示
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(const SnackBar(content: Text('用户信息编辑功能开发中')));
              },
              icon: const Icon(Icons.edit, color: Colors.white),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建通知设置区域
  Widget _buildNotificationSection(
    BuildContext context,
    WidgetRef ref,
    AppSettings settings,
  ) {
    return _buildSettingsSection(
      title: '通知设置',
      icon: Icons.notifications,
      children: [
        SwitchListTile(
          title: const Text('启用通知'),
          subtitle: const Text('接收优化建议和提醒'),
          value: settings.enableNotifications,
          activeColor: const Color(0xFF2F76DA),
          onChanged: (value) =>
              ref.read(settingsProvider.notifier).toggleNotifications(),
        ),
        SwitchListTile(
          title: const Text('节能提示'),
          subtitle: const Text('定期推送节能建议'),
          value: settings.enableEnergyTips,
          activeColor: const Color(0xFF2F76DA),
          onChanged: (value) =>
              ref.read(settingsProvider.notifier).toggleEnergyTips(),
        ),
      ],
    );
  }

  /// 构建计算设置区域
  Widget _buildCalculationSection(
    BuildContext context,
    WidgetRef ref,
    AppSettings settings,
  ) {
    return _buildSettingsSection(
      title: '计算设置',
      icon: Icons.calculate,
      children: [
        SwitchListTile(
          title: const Text('自动计算'),
          subtitle: const Text('参数变化时自动重新计算'),
          value: settings.enableAutoCalculation,
          activeColor: const Color(0xFF2F76DA),
          onChanged: (value) =>
              ref.read(settingsProvider.notifier).toggleAutoCalculation(),
        ),
        ListTile(
          title: const Text('默认温度'),
          subtitle: Text(
            '${settings.defaultTemperature.toStringAsFixed(1)}${settings.temperatureUnit}',
          ),
          trailing: const Icon(Icons.chevron_right),
          onTap: () => _showTemperatureDialog(context, ref, settings),
        ),
        ListTile(
          title: const Text('温度容差'),
          subtitle: Text(
            '±${settings.temperatureTolerance.toStringAsFixed(1)}${settings.temperatureUnit}',
          ),
          trailing: const Icon(Icons.chevron_right),
          onTap: () => _showToleranceDialog(context, ref, settings),
        ),
        ListTile(
          title: const Text('温度单位'),
          subtitle: Text(settings.temperatureUnit),
          trailing: const Icon(Icons.chevron_right),
          onTap: () => _showUnitDialog(context, ref, settings),
        ),
      ],
    );
  }

  /// 构建界面设置区域
  Widget _buildInterfaceSection(
    BuildContext context,
    WidgetRef ref,
    AppSettings settings,
  ) {
    return _buildSettingsSection(
      title: '界面设置',
      icon: Icons.palette,
      children: [
        SwitchListTile(
          title: const Text('深色模式'),
          subtitle: const Text('使用深色主题'),
          value: settings.enableDarkMode,
          activeColor: const Color(0xFF2F76DA),
          onChanged: (value) =>
              ref.read(settingsProvider.notifier).toggleDarkMode(),
        ),
        ListTile(
          title: const Text('语言'),
          subtitle: Text(settings.language),
          trailing: const Icon(Icons.chevron_right),
          onTap: () => _showLanguageDialog(context, ref, settings),
        ),
        ListTile(
          title: const Text('字体大小'),
          subtitle: Text('${settings.fontSize.toStringAsFixed(0)}pt'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () => _showFontSizeDialog(context, ref, settings),
        ),
        SwitchListTile(
          title: const Text('触觉反馈'),
          subtitle: const Text('操作时的震动反馈'),
          value: settings.enableHapticFeedback,
          activeColor: const Color(0xFF2F76DA),
          onChanged: (value) =>
              ref.read(settingsProvider.notifier).toggleHapticFeedback(),
        ),
        SwitchListTile(
          title: const Text('声音效果'),
          subtitle: const Text('操作时的声音反馈'),
          value: settings.enableSounds,
          activeColor: const Color(0xFF2F76DA),
          onChanged: (value) =>
              ref.read(settingsProvider.notifier).toggleSounds(),
        ),
      ],
    );
  }

  /// 构建数据和隐私设置区域
  Widget _buildDataPrivacySection(
    BuildContext context,
    WidgetRef ref,
    AppSettings settings,
  ) {
    return _buildSettingsSection(
      title: '数据和隐私',
      icon: Icons.security,
      children: [
        SwitchListTile(
          title: const Text('数据同步'),
          subtitle: const Text('同步设置到云端'),
          value: settings.enableDataSync,
          activeColor: const Color(0xFF2F76DA),
          onChanged: (value) =>
              ref.read(settingsProvider.notifier).toggleDataSync(),
        ),
        SwitchListTile(
          title: const Text('数据分析'),
          subtitle: const Text('帮助改进应用体验'),
          value: settings.enableAnalytics,
          activeColor: const Color(0xFF2F76DA),
          onChanged: (value) =>
              ref.read(settingsProvider.notifier).toggleAnalytics(),
        ),
        ListTile(
          title: const Text('清除缓存'),
          subtitle: const Text('清除临时数据'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () => _showClearCacheDialog(context),
        ),
        ListTile(
          title: const Text('隐私政策'),
          subtitle: const Text('查看隐私保护政策'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () => _showPrivacyPolicy(context),
        ),
      ],
    );
  }

  /// 构建关于应用区域
  Widget _buildAboutSection(BuildContext context) {
    return _buildSettingsSection(
      title: '关于应用',
      icon: Icons.info,
      children: [
        ListTile(
          title: const Text('版本信息'),
          subtitle: const Text('v1.0.0 (Build 1)'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () => _showVersionInfo(context),
        ),
        ListTile(
          title: const Text('检查更新'),
          subtitle: const Text('查看是否有新版本'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () => _checkForUpdates(context),
        ),
        ListTile(
          title: const Text('用户反馈'),
          subtitle: const Text('提交建议和问题'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () => _showFeedback(context),
        ),
        ListTile(
          title: const Text('开源许可'),
          subtitle: const Text('查看开源组件许可'),
          trailing: const Icon(Icons.chevron_right),
          onTap: () => _showLicenses(context),
        ),
      ],
    );
  }

  /// 构建设置分组
  Widget _buildSettingsSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(icon, color: const Color(0xFF2F76DA), size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF37352F),
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  /// 显示重置对话框
  void _showResetDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('重置设置'),
        content: const Text('确定要重置所有设置为默认值吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              ref.read(settingsProvider.notifier).resetAllSettings();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(const SnackBar(content: Text('设置已重置')));
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 显示导出对话框
  void _showExportDialog(BuildContext context) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('导出功能开发中')));
  }

  /// 显示导入对话框
  void _showImportDialog(BuildContext context) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('导入功能开发中')));
  }

  /// 显示温度设置对话框
  void _showTemperatureDialog(
    BuildContext context,
    WidgetRef ref,
    AppSettings settings,
  ) {
    double tempValue = settings.defaultTemperature;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: const Text('默认温度'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '${tempValue.toStringAsFixed(1)}${settings.temperatureUnit}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 16),
                Slider(
                  value: tempValue,
                  min: 16.0,
                  max: 30.0,
                  divisions: 28,
                  activeColor: const Color(0xFF2F76DA),
                  onChanged: (value) {
                    setState(() {
                      tempValue = value;
                    });
                  },
                ),
                const SizedBox(height: 8),
                const Text(
                  '推荐范围：22-26°C',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () {
                  // 保存温度设置
                  ref
                      .read(settingsProvider.notifier)
                      .updateDefaultTemperature(tempValue);

                  Navigator.of(context).pop();

                  // 显示保存成功提示
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        '默认温度已设置为 ${tempValue.toStringAsFixed(1)}${settings.temperatureUnit}',
                      ),
                      backgroundColor: const Color(0xFF4CAF50),
                    ),
                  );
                },
                child: const Text('确定'),
              ),
            ],
          );
        },
      ),
    );
  }

  /// 显示容差设置对话框
  void _showToleranceDialog(
    BuildContext context,
    WidgetRef ref,
    AppSettings settings,
  ) {
    // TODO: 实现容差设置对话框
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('容差设置功能开发中')));
  }

  /// 显示单位设置对话框
  void _showUnitDialog(
    BuildContext context,
    WidgetRef ref,
    AppSettings settings,
  ) {
    // TODO: 实现单位设置对话框
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('单位设置功能开发中')));
  }

  /// 显示语言设置对话框
  void _showLanguageDialog(
    BuildContext context,
    WidgetRef ref,
    AppSettings settings,
  ) {
    // TODO: 实现语言设置对话框
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('语言设置功能开发中')));
  }

  /// 显示字体大小设置对话框
  void _showFontSizeDialog(
    BuildContext context,
    WidgetRef ref,
    AppSettings settings,
  ) {
    // TODO: 实现字体大小设置对话框
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('字体大小设置功能开发中')));
  }

  /// 显示清除缓存对话框
  void _showClearCacheDialog(BuildContext context) {
    // TODO: 实现清除缓存功能
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('清除缓存功能开发中')));
  }

  /// 显示隐私政策
  void _showPrivacyPolicy(BuildContext context) {
    // TODO: 显示隐私政策页面
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('隐私政策页面开发中')));
  }

  /// 显示版本信息
  void _showVersionInfo(BuildContext context) {
    // TODO: 显示详细版本信息
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('版本信息页面开发中')));
  }

  /// 检查更新
  void _checkForUpdates(BuildContext context) {
    // TODO: 实现更新检查功能
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('检查更新功能开发中')));
  }

  /// 显示用户反馈
  void _showFeedback(BuildContext context) {
    // TODO: 显示反馈页面
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('用户反馈功能开发中')));
  }

  /// 显示开源许可
  void _showLicenses(BuildContext context) {
    showLicensePage(context: context);
  }
}
