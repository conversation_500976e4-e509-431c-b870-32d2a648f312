import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:oneday/features/wage_system/services/premium_article_service.dart';

void main() {
  group('PremiumArticleService Tests', () {
    late PremiumArticleService service;

    setUp(() async {
      // 设置测试环境
      SharedPreferences.setMockInitialValues({});
      service = PremiumArticleService.instance;
      await service.initialize();
      await service.resetPremiumAccess(); // 确保测试开始时状态干净
    });

    test('初始状态应该没有优质文章访问权限', () {
      expect(service.hasPremiumAccess, false);
      expect(service.activationHistory, isEmpty);
      expect(service.getCurrentActiveRecord(), isNull);
    });

    test('文章类型判断逻辑应该正确', () {
      // 优质文章
      expect(service.isArticlePremium('premium_article_1'), true);
      expect(service.isArticlePremium('1_some_article'), true);
      expect(service.isArticlePremium('2_another_article'), true);
      expect(service.isArticlePremium('3_third_article'), true);
      
      // 普通文章
      expect(service.isArticlePremium('4_normal_article'), false);
      expect(service.isArticlePremium('normal_article'), false);
      expect(service.isArticlePremium('5_regular_post'), false);
    });

    test('没有权限时应该拒绝访问优质文章', () async {
      final result = await service.validateArticleAccess('premium_article_1');
      
      expect(result.isAllowed, false);
      expect(result.isPremium, false);
      expect(result.deniedReason, isNotNull);
      expect(result.suggestedAction, isNotNull);
    });

    test('普通文章应该始终允许访问', () async {
      final result = await service.validateArticleAccess('normal_article_5');
      
      expect(result.isAllowed, true);
      expect(result.isPremium, false);
      expect(result.deniedReason, isNull);
    });

    test('激活永久优质文章访问权限', () async {
      final success = await service.activatePremiumAccess(
        activationType: PremiumActivationType.purchase,
        itemId: '14',
        metadata: {'price': 300.0},
      );

      expect(success, true);
      expect(service.hasPremiumAccess, true);
      
      final activeRecord = service.getCurrentActiveRecord();
      expect(activeRecord, isNotNull);
      expect(activeRecord!.isPermanent, true);
      expect(activeRecord.activationType, PremiumActivationType.purchase);
      expect(activeRecord.itemId, '14');
    });

    test('激活权限后应该允许访问优质文章', () async {
      // 先激活权限
      await service.activatePremiumAccess(
        activationType: PremiumActivationType.purchase,
        itemId: '14',
      );

      // 测试优质文章访问
      final result = await service.validateArticleAccess('premium_article_1');
      
      expect(result.isAllowed, true);
      expect(result.isPremium, true);
      expect(result.activationRecord, isNotNull);
    });

    test('临时权限应该正确处理', () async {
      final success = await service.activatePremiumAccess(
        activationType: PremiumActivationType.trial,
        duration: const Duration(hours: 1),
      );

      expect(success, true);
      expect(service.hasPremiumAccess, true);
      
      final activeRecord = service.getCurrentActiveRecord();
      expect(activeRecord, isNotNull);
      expect(activeRecord!.isPermanent, false);
      expect(activeRecord.getRemainingTime(), isNotNull);
      expect(activeRecord.activationType, PremiumActivationType.trial);
    });

    test('权限状态应该持久化', () async {
      // 激活权限
      await service.activatePremiumAccess(
        activationType: PremiumActivationType.purchase,
        itemId: '14',
      );

      expect(service.hasPremiumAccess, true);

      // 重新初始化服务（模拟应用重启）
      final newService = PremiumArticleService.instance;
      await newService.initialize();

      expect(newService.hasPremiumAccess, true);
      expect(newService.activationHistory, isNotEmpty);
    });

    test('激活历史应该正确记录', () async {
      // 激活多次权限
      await service.activatePremiumAccess(
        activationType: PremiumActivationType.trial,
        duration: const Duration(hours: 1),
      );

      await service.activatePremiumAccess(
        activationType: PremiumActivationType.purchase,
        itemId: '14',
      );

      final history = service.activationHistory;
      expect(history.length, 2);
      expect(history.first.activationType, PremiumActivationType.purchase); // 最新的在前
      expect(history.last.activationType, PremiumActivationType.trial);
    });

    test('重置权限应该清除所有状态', () async {
      // 先激活权限
      await service.activatePremiumAccess(
        activationType: PremiumActivationType.purchase,
        itemId: '14',
      );

      expect(service.hasPremiumAccess, true);

      // 重置权限
      await service.resetPremiumAccess();

      expect(service.hasPremiumAccess, false);
      expect(service.activationHistory, isEmpty);
      expect(service.getCurrentActiveRecord(), isNull);
    });
  });
}
