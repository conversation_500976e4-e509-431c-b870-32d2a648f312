import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

// 生成Mock类
@GenerateMocks([ImageCropper])
import 'image_cropper_test.mocks.dart';

void main() {
  group('ImageCropper 功能测试', () {
    late MockImageCropper mockImageCropper;

    setUp(() {
      mockImageCropper = MockImageCropper();
    });

    testWidgets('应该正确配置裁剪参数', (WidgetTester tester) async {
      // 模拟成功的裁剪结果
      final mockCroppedFile = CroppedFile('/path/to/cropped/image.jpg');
      
      when(mockImageCropper.cropImage(
        sourcePath: anyNamed('sourcePath'),
        compressFormat: anyNamed('compressFormat'),
        compressQuality: anyNamed('compressQuality'),
        uiSettings: anyNamed('uiSettings'),
      )).thenAnswer((_) async => mockCroppedFile);

      // 测试裁剪配置
      final result = await mockImageCropper.cropImage(
        sourcePath: '/path/to/source/image.jpg',
        compressFormat: ImageCompressFormat.jpg,
        compressQuality: 85,
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: '裁剪头像',
            toolbarColor: const Color(0xFF2F76DA),
            toolbarWidgetColor: Colors.white,
            backgroundColor: Colors.white,
            activeControlsWidgetColor: const Color(0xFF2F76DA),
            initAspectRatio: CropAspectRatioPreset.square,
            lockAspectRatio: true,
            aspectRatioPresets: [CropAspectRatioPreset.square],
            showCropGrid: false,
            hideBottomControls: false,
          ),
          IOSUiSettings(
            title: '裁剪头像',
            doneButtonTitle: '完成',
            cancelButtonTitle: '取消',
            aspectRatioLockEnabled: true,
            resetAspectRatioEnabled: false,
            aspectRatioPickerButtonHidden: true,
            resetButtonHidden: true,
            rotateButtonsHidden: false,
            aspectRatioPresets: [CropAspectRatioPreset.square],
          ),
        ],
      );

      expect(result, isNotNull);
      expect(result!.path, '/path/to/cropped/image.jpg');
      
      // 验证调用参数
      verify(mockImageCropper.cropImage(
        sourcePath: '/path/to/source/image.jpg',
        compressFormat: ImageCompressFormat.jpg,
        compressQuality: 85,
        uiSettings: anyNamed('uiSettings'),
      )).called(1);
    });

    test('应该正确处理裁剪取消', () async {
      // 模拟用户取消裁剪
      when(mockImageCropper.cropImage(
        sourcePath: anyNamed('sourcePath'),
        compressFormat: anyNamed('compressFormat'),
        compressQuality: anyNamed('compressQuality'),
        uiSettings: anyNamed('uiSettings'),
      )).thenAnswer((_) async => null);

      final result = await mockImageCropper.cropImage(
        sourcePath: '/path/to/source/image.jpg',
        compressFormat: ImageCompressFormat.jpg,
        compressQuality: 85,
        uiSettings: [],
      );

      expect(result, isNull);
    });

    test('应该正确处理裁剪错误', () async {
      // 模拟裁剪错误
      when(mockImageCropper.cropImage(
        sourcePath: anyNamed('sourcePath'),
        compressFormat: anyNamed('compressFormat'),
        compressQuality: anyNamed('compressQuality'),
        uiSettings: anyNamed('uiSettings'),
      )).thenThrow(Exception('裁剪失败'));

      expect(
        () async => await mockImageCropper.cropImage(
          sourcePath: '/path/to/source/image.jpg',
          compressFormat: ImageCompressFormat.jpg,
          compressQuality: 85,
          uiSettings: [],
        ),
        throwsException,
      );
    });

    group('裁剪配置验证', () {
      test('Android配置应该包含必要参数', () {
        final androidSettings = AndroidUiSettings(
          toolbarTitle: '裁剪头像',
          toolbarColor: const Color(0xFF2F76DA),
          toolbarWidgetColor: Colors.white,
          backgroundColor: Colors.white,
          activeControlsWidgetColor: const Color(0xFF2F76DA),
          initAspectRatio: CropAspectRatioPreset.square,
          lockAspectRatio: true,
          aspectRatioPresets: [CropAspectRatioPreset.square],
          showCropGrid: false,
          hideBottomControls: false,
        );

        expect(androidSettings.toolbarTitle, '裁剪头像');
        expect(androidSettings.toolbarColor, const Color(0xFF2F76DA));
        expect(androidSettings.lockAspectRatio, true);
        expect(androidSettings.aspectRatioPresets, [CropAspectRatioPreset.square]);
      });

      test('iOS配置应该包含必要参数', () {
        final iosSettings = IOSUiSettings(
          title: '裁剪头像',
          doneButtonTitle: '完成',
          cancelButtonTitle: '取消',
          aspectRatioLockEnabled: true,
          resetAspectRatioEnabled: false,
          aspectRatioPickerButtonHidden: true,
          resetButtonHidden: true,
          rotateButtonsHidden: false,
          aspectRatioPresets: [CropAspectRatioPreset.square],
        );

        expect(iosSettings.title, '裁剪头像');
        expect(iosSettings.doneButtonTitle, '完成');
        expect(iosSettings.cancelButtonTitle, '取消');
        expect(iosSettings.aspectRatioLockEnabled, true);
        expect(iosSettings.aspectRatioPresets, [CropAspectRatioPreset.square]);
      });
    });

    group('文件处理测试', () {
      test('应该验证源文件存在性', () async {
        // 这个测试需要在实际环境中运行
        // 这里只是展示测试结构
        const sourcePath = '/path/to/nonexistent/image.jpg';
        
        // 在实际实现中，应该检查文件是否存在
        final file = File(sourcePath);
        expect(await file.exists(), false);
      });

      test('应该验证裁剪结果文件', () async {
        // 模拟裁剪成功
        final mockCroppedFile = CroppedFile('/path/to/cropped/image.jpg');
        
        when(mockImageCropper.cropImage(
          sourcePath: anyNamed('sourcePath'),
          compressFormat: anyNamed('compressFormat'),
          compressQuality: anyNamed('compressQuality'),
          uiSettings: anyNamed('uiSettings'),
        )).thenAnswer((_) async => mockCroppedFile);

        final result = await mockImageCropper.cropImage(
          sourcePath: '/path/to/source/image.jpg',
          compressFormat: ImageCompressFormat.jpg,
          compressQuality: 85,
          uiSettings: [],
        );

        expect(result, isNotNull);
        expect(result!.path, isNotEmpty);
        expect(result.path.endsWith('.jpg'), true);
      });
    });

    group('压缩质量测试', () {
      test('应该使用正确的压缩质量', () async {
        final mockCroppedFile = CroppedFile('/path/to/cropped/image.jpg');
        
        when(mockImageCropper.cropImage(
          sourcePath: anyNamed('sourcePath'),
          compressFormat: ImageCompressFormat.jpg,
          compressQuality: 85,
          uiSettings: anyNamed('uiSettings'),
        )).thenAnswer((_) async => mockCroppedFile);

        await mockImageCropper.cropImage(
          sourcePath: '/path/to/source/image.jpg',
          compressFormat: ImageCompressFormat.jpg,
          compressQuality: 85,
          uiSettings: [],
        );

        verify(mockImageCropper.cropImage(
          sourcePath: anyNamed('sourcePath'),
          compressFormat: ImageCompressFormat.jpg,
          compressQuality: 85,
          uiSettings: anyNamed('uiSettings'),
        )).called(1);
      });

      test('应该使用JPEG格式', () async {
        final mockCroppedFile = CroppedFile('/path/to/cropped/image.jpg');
        
        when(mockImageCropper.cropImage(
          sourcePath: anyNamed('sourcePath'),
          compressFormat: ImageCompressFormat.jpg,
          compressQuality: anyNamed('compressQuality'),
          uiSettings: anyNamed('uiSettings'),
        )).thenAnswer((_) async => mockCroppedFile);

        await mockImageCropper.cropImage(
          sourcePath: '/path/to/source/image.jpg',
          compressFormat: ImageCompressFormat.jpg,
          compressQuality: 85,
          uiSettings: [],
        );

        verify(mockImageCropper.cropImage(
          sourcePath: anyNamed('sourcePath'),
          compressFormat: ImageCompressFormat.jpg,
          compressQuality: anyNamed('compressQuality'),
          uiSettings: anyNamed('uiSettings'),
        )).called(1);
      });
    });
  });
}
