import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/vocabulary/word_model.dart';

void main() {
  group('单词详情弹窗测试', () {
    late WordDetails testWordDetails;

    setUp(() {
      testWordDetails = WordDetails(
        id: 1,
        definition: '测试单词的定义',
        frequency: 100,
        difficulty: 'intermediate',
        category: 'test',
        alternativeSpellings: [],
        partOfSpeech: 'noun',
        examples: ['This is a test example.', 'Another test example.'],
        tags: ['test'],
        priority: 'high',
        phonetic: '/test/',
        synonyms: ['exam', 'trial'],
        antonyms: ['real'],
        etymology: 'From Latin testum',
      );
    });

    testWidgets('应该显示单词详情弹窗的基本信息', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => _WordDetailDialog(
                      word: 'test',
                      details: testWordDetails,
                      onAddToMemory: () {
                        // 这个测试只验证显示，不需要回调逻辑
                      },
                    ),
                  );
                },
                child: const Text('Show Dialog'),
              ),
            ),
          ),
        ),
      );

      // 点击按钮显示弹窗
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // 验证弹窗内容
      expect(find.text('test'), findsOneWidget);
      expect(find.text('/test/'), findsOneWidget);
      expect(find.text('测试单词的定义'), findsOneWidget);
      expect(find.text('noun'), findsOneWidget);
      expect(find.text('intermediate'), findsOneWidget);
      expect(find.text('频率: 100'), findsOneWidget);

      // 验证例句
      expect(find.text('This is a test example.'), findsOneWidget);
      expect(find.text('Another test example.'), findsOneWidget);

      // 验证同义词和反义词
      expect(find.text('exam, trial'), findsOneWidget);
      expect(find.text('real'), findsOneWidget);

      // 验证词源
      expect(find.text('From Latin testum'), findsOneWidget);

      // 验证按钮
      expect(find.text('关闭'), findsOneWidget);
      expect(find.text('加入记忆'), findsOneWidget);
    });

    testWidgets('点击加入记忆按钮应该调用回调函数', (WidgetTester tester) async {
      bool addToMemoryCalled = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => _WordDetailDialog(
                      word: 'test',
                      details: testWordDetails,
                      onAddToMemory: () {
                        addToMemoryCalled = true;
                      },
                    ),
                  );
                },
                child: const Text('Show Dialog'),
              ),
            ),
          ),
        ),
      );

      // 显示弹窗
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // 点击加入记忆按钮
      await tester.tap(find.text('加入记忆'));
      await tester.pumpAndSettle();

      // 验证回调被调用
      expect(addToMemoryCalled, isTrue);
    });

    testWidgets('点击关闭按钮应该关闭弹窗', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => _WordDetailDialog(
                      word: 'test',
                      details: testWordDetails,
                      onAddToMemory: () {},
                    ),
                  );
                },
                child: const Text('Show Dialog'),
              ),
            ),
          ),
        ),
      );

      // 显示弹窗
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // 验证弹窗存在
      expect(find.text('test'), findsOneWidget);

      // 点击关闭按钮
      await tester.tap(find.text('关闭'));
      await tester.pumpAndSettle();

      // 验证弹窗已关闭
      expect(find.text('test'), findsNothing);
    });

    testWidgets('应该正确显示Notion风格设计', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => _WordDetailDialog(
                      word: 'test',
                      details: testWordDetails,
                      onAddToMemory: () {},
                    ),
                  );
                },
                child: const Text('Show Dialog'),
              ),
            ),
          ),
        ),
      );

      // 显示弹窗
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // 查找弹窗容器
      final dialogContainer = tester.widget<Container>(
        find.descendant(
          of: find.byType(Dialog),
          matching: find.byType(Container).first,
        ),
      );

      // 验证Notion风格设计
      final decoration = dialogContainer.decoration as BoxDecoration;
      expect(decoration.color, Colors.white);
      expect(decoration.borderRadius, BorderRadius.circular(12));
      expect(decoration.boxShadow, isNotNull);
    });
  });
}

// 为了测试需要，创建一个简化的_WordDetailDialog类
class _WordDetailDialog extends StatelessWidget {
  final String word;
  final WordDetails details;
  final VoidCallback onAddToMemory;

  const _WordDetailDialog({
    required this.word,
    required this.details,
    required this.onAddToMemory,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: BoxConstraints(
          maxWidth: 500,
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题栏
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                border: Border(bottom: BorderSide(color: Color(0xFFE3E2E0))),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          word,
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF37352F),
                          ),
                        ),
                        if (details.phonetic != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            details.phonetic!,
                            style: const TextStyle(
                              fontSize: 16,
                              color: Color(0xFF9B9A97),
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Color(0xFF9B9A97)),
                  ),
                ],
              ),
            ),

            // 内容区域（简化版用于测试）
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 基本信息标签
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        Text(details.partOfSpeech),
                        Text(details.difficulty),
                        if (details.frequency > 0)
                          Text('频率: ${details.frequency}'),
                      ],
                    ),
                    const SizedBox(height: 20),
                    Text(details.definition),
                    if (details.examples.isNotEmpty) ...[
                      const SizedBox(height: 20),
                      ...details.examples.map((example) => Text(example)),
                    ],
                    if (details.synonyms.isNotEmpty) ...[
                      const SizedBox(height: 20),
                      Text(details.synonyms.join(', ')),
                    ],
                    if (details.antonyms.isNotEmpty) ...[
                      const SizedBox(height: 20),
                      Text(details.antonyms.join(', ')),
                    ],
                    if (details.etymology != null) ...[
                      const SizedBox(height: 20),
                      Text(details.etymology!),
                    ],
                  ],
                ),
              ),
            ),

            // 底部操作栏
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('关闭'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        onAddToMemory();
                        Navigator.of(context).pop();
                      },
                      child: const Text('加入记忆'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
