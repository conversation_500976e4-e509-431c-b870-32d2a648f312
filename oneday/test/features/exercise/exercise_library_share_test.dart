import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/exercise/custom_exercise_category.dart';
import 'package:oneday/features/community/community_storage_service.dart';
import 'package:oneday/features/community/community_feed_page.dart';
import 'package:oneday/core/data/pao_exercises_data.dart';

/// 测试动作库分类共享功能
void main() {
  // 初始化测试绑定
  TestWidgetsFlutterBinding.ensureInitialized();
  group('Exercise Library Share Tests', () {
    late CommunityStorageService storageService;

    setUp(() async {
      storageService = CommunityStorageService.instance;
      await storageService.initialize();
      // 清空测试数据
      await storageService.clearAllPosts();
    });

    test('should create community post when sharing category', () async {
      // 创建测试分类
      final testCategory = CustomExerciseCategory(
        id: 'test_category_1',
        name: '测试健身分类',
        icon: '🏃‍♂️',
        description: '这是一个测试用的健身分类',
        exercises: {
          'A': PAOExercise(
            letter: 'A',
            nameCn: '俯卧撑',
            nameEn: 'Push-up',
            description: '标准俯卧撑动作',
            category: '健身',
            scene: '简单活动',
            keywords: ['action', 'arm'],
          ),
          'B': PAOExercise(
            letter: 'B',
            nameCn: '深蹲',
            nameEn: 'Squat',
            description: '标准深蹲动作',
            category: '健身',
            scene: '集中训练',
            keywords: ['body', 'build'],
          ),
        },
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );

      // 模拟上传分类到社区的逻辑
      final postId = await storageService.getNextPostId();

      final currentUser = UserInfo(
        id: 1,
        username: '动作库创作者',
        avatar: '',
        isVerified: false,
      );

      // 构建分类信息内容
      final categoryInfo = StringBuffer();
      categoryInfo.writeln('🏃‍♂️ 动作库分类分享');
      categoryInfo.writeln('');
      categoryInfo.writeln('📂 分类名称：${testCategory.name}');
      categoryInfo.writeln(
        '📝 分类描述：${testCategory.description?.isNotEmpty == true ? testCategory.description : '暂无描述'}',
      );
      categoryInfo.writeln('🎯 动作数量：${testCategory.exercises.length} 个');
      categoryInfo.writeln('');

      if (testCategory.exercises.isNotEmpty) {
        categoryInfo.writeln('💪 包含动作：');
        int count = 0;
        for (final exercise in testCategory.exercises.values) {
          if (count >= 5) {
            categoryInfo.writeln(
              '... 还有 ${testCategory.exercises.length - 5} 个动作',
            );
            break;
          }
          categoryInfo.writeln(
            '• ${exercise.letter} - ${exercise.name} (${exercise.nameEn})',
          );
          count++;
        }
        categoryInfo.writeln('');
      }

      categoryInfo.writeln('🔗 来自OneDay动作库管理系统');
      categoryInfo.writeln('');
      categoryInfo.writeln('#动作库分类 #动觉记忆 #健身运动 #学习方法');

      // 创建社区帖子
      final newPost = CommunityPost(
        id: postId,
        author: currentUser,
        content: categoryInfo.toString(),
        type: PostType.experience,
        tags: ['动作库分类', '动觉记忆', '健身运动', '学习方法'],
        images: [],
        likeCount: 0,
        commentCount: 0,
        shareCount: 0,
        createdAt: DateTime.now(),
        isLiked: false,
      );

      // 保存到社区存储
      final success = await storageService.addPost(newPost);

      // 验证结果
      expect(success, isTrue);

      // 验证帖子是否正确保存
      final allPosts = await storageService.getAllPosts();
      expect(allPosts.length, equals(1));

      final savedPost = allPosts.first;
      expect(savedPost.id, equals(postId));
      expect(savedPost.author.username, equals('动作库创作者'));
      expect(savedPost.type, equals(PostType.experience));
      expect(savedPost.tags, contains('动作库分类'));
      expect(savedPost.content, contains('测试健身分类'));
      expect(savedPost.content, contains('俯卧撑'));
      expect(savedPost.content, contains('深蹲'));
    });

    test('should handle empty category description', () async {
      // 创建没有描述的测试分类
      final testCategory = CustomExerciseCategory(
        id: 'test_category_2',
        name: '无描述分类',
        icon: '📝',
        description: null, // 测试null描述
        exercises: {},
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );

      // 测试描述处理逻辑
      final description = testCategory.description?.isNotEmpty == true
          ? testCategory.description
          : '暂无描述';

      expect(description, equals('暂无描述'));
    });

    test('should handle category with many exercises', () async {
      // 创建包含多个动作的分类
      final exercises = <String, PAOExercise>{};
      for (int i = 0; i < 10; i++) {
        final letter = String.fromCharCode(65 + i); // A-J
        exercises[letter] = PAOExercise(
          letter: letter,
          nameCn: '动作$i',
          nameEn: 'Exercise$i',
          description: '测试动作$i',
          category: '测试',
          scene: '简单活动',
          keywords: ['test$i'],
        );
      }

      final testCategory = CustomExerciseCategory(
        id: 'test_category_3',
        name: '多动作分类',
        icon: '💪',
        description: '包含多个动作的测试分类',
        exercises: exercises,
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );

      // 验证动作数量
      expect(testCategory.exercises.length, equals(10));

      // 模拟内容生成逻辑，验证只显示前5个动作
      final categoryInfo = StringBuffer();
      if (testCategory.exercises.isNotEmpty) {
        categoryInfo.writeln('💪 包含动作：');
        int count = 0;
        for (final exercise in testCategory.exercises.values) {
          if (count >= 5) {
            categoryInfo.writeln(
              '... 还有 ${testCategory.exercises.length - 5} 个动作',
            );
            break;
          }
          categoryInfo.writeln(
            '• ${exercise.letter} - ${exercise.name} (${exercise.nameEn})',
          );
          count++;
        }
      }

      final content = categoryInfo.toString();
      expect(content, contains('... 还有 5 个动作'));
    });

    test('should create community post when sharing system category', () async {
      // 测试系统默认分类（如篮球）的共享功能
      final categoryName = '篮球';

      // 获取系统分类的动作数据
      final categoryExercises = PAOExercisesData.getExercisesByCategory(
        categoryName,
      );
      expect(categoryExercises, isNotNull);
      expect(categoryExercises!.isNotEmpty, isTrue);

      // 模拟系统分类共享的逻辑
      final postId = await storageService.getNextPostId();

      final currentUser = UserInfo(
        id: 1,
        username: '动作库创作者',
        avatar: '',
        isVerified: false,
      );

      // 构建分类信息内容
      final categoryInfo = StringBuffer();
      categoryInfo.writeln('🏃‍♂️ 系统动作库分类分享');
      categoryInfo.writeln('');
      categoryInfo.writeln('📂 分类名称：$categoryName');
      categoryInfo.writeln('📝 分类类型：系统内置分类');
      categoryInfo.writeln('🎯 动作数量：${categoryExercises.length} 个');
      categoryInfo.writeln('');

      if (categoryExercises.isNotEmpty) {
        categoryInfo.writeln('💪 包含动作：');
        int count = 0;
        for (final exercise in categoryExercises.values) {
          if (count >= 5) {
            categoryInfo.writeln('... 还有 ${categoryExercises.length - 5} 个动作');
            break;
          }
          categoryInfo.writeln(
            '• ${exercise.letter} - ${exercise.name} (${exercise.nameEn})',
          );
          count++;
        }
        categoryInfo.writeln('');
      }

      categoryInfo.writeln('🔗 来自OneDay系统动作库');
      categoryInfo.writeln('');
      categoryInfo.writeln('#系统动作库 #$categoryName #动觉记忆 #学习方法');

      // 创建社区帖子
      final newPost = CommunityPost(
        id: postId,
        author: currentUser,
        content: categoryInfo.toString(),
        type: PostType.experience,
        tags: ['系统动作库', categoryName, '动觉记忆', '学习方法'],
        images: [],
        likeCount: 0,
        commentCount: 0,
        shareCount: 0,
        createdAt: DateTime.now(),
        isLiked: false,
      );

      // 保存到社区存储
      final success = await storageService.addPost(newPost);

      // 验证结果
      expect(success, isTrue);

      // 验证帖子是否正确保存
      final allPosts = await storageService.getAllPosts();
      expect(allPosts.length, equals(1));

      final savedPost = allPosts.first;
      expect(savedPost.id, equals(postId));
      expect(savedPost.author.username, equals('动作库创作者'));
      expect(savedPost.type, equals(PostType.experience));
      expect(savedPost.tags, contains('系统动作库'));
      expect(savedPost.tags, contains(categoryName));
      expect(savedPost.content, contains('系统动作库分类分享'));
      expect(savedPost.content, contains(categoryName));
      expect(savedPost.content, contains('系统内置分类'));
    });

    test('should handle different system categories', () async {
      // 测试不同的系统分类
      final testCategories = ['健身', '瑜伽', '养生', '篮球', '足球', '拉伸', '护眼'];

      for (final categoryName in testCategories) {
        final exercises = PAOExercisesData.getExercisesByCategory(categoryName);

        // 验证每个分类都有动作数据
        expect(exercises, isNotNull, reason: '分类 $categoryName 应该有动作数据');
        expect(
          exercises!.isNotEmpty,
          isTrue,
          reason: '分类 $categoryName 应该包含动作',
        );

        // 验证动作数据的完整性
        for (final exercise in exercises.values) {
          expect(exercise.category, equals(categoryName));
          expect(exercise.letter.isNotEmpty, isTrue);
          expect(exercise.name.isNotEmpty, isTrue);
          expect(exercise.nameEn.isNotEmpty, isTrue);
        }
      }
    });
  });
}
