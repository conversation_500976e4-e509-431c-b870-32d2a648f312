import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:oneday/features/community/community_storage_service.dart';
import 'package:oneday/features/community/community_feed_page.dart';

void main() {
  group('CommunityStorageService Tests', () {
    late CommunityStorageService storageService;

    setUp(() async {
      // 设置测试环境
      SharedPreferences.setMockInitialValues({});
      storageService = CommunityStorageService.instance;
      await storageService.initialize();
    });

    test('应该能够添加和获取帖子', () async {
      // 创建测试帖子
      final testPost = CommunityPost(
        id: 1001,
        author: UserInfo(
          id: 1,
          username: '测试用户',
          avatar: 'test_avatar.jpg',
          isVerified: false,
        ),
        content: '这是一个测试帖子\n\n测试内容',
        type: PostType.study,
        tags: ['测试', '学习'],
        images: [],
        likeCount: 0,
        commentCount: 0,
        shareCount: 0,
        createdAt: DateTime.now(),
        isLiked: false,
        isPremium: false,
      );

      // 添加帖子
      final addResult = await storageService.addPost(testPost);
      expect(addResult, true);

      // 获取所有帖子
      final posts = await storageService.getAllPosts();
      expect(posts.length, 1);
      expect(posts.first.id, 1001);
      expect(posts.first.content, '这是一个测试帖子\n\n测试内容');
      expect(posts.first.author.username, '测试用户');
    });

    test('应该能够更新帖子', () async {
      // 创建测试帖子
      final testPost = CommunityPost(
        id: 1002,
        author: UserInfo(
          id: 1,
          username: '测试用户',
          avatar: 'test_avatar.jpg',
          isVerified: false,
        ),
        content: '原始内容',
        type: PostType.study,
        tags: ['测试'],
        images: [],
        likeCount: 0,
        commentCount: 0,
        shareCount: 0,
        createdAt: DateTime.now(),
        isLiked: false,
      );

      // 添加帖子
      await storageService.addPost(testPost);

      // 更新帖子（点赞）
      testPost.isLiked = true;
      testPost.likeCount = 1;
      final updateResult = await storageService.updatePost(testPost);
      expect(updateResult, true);

      // 验证更新
      final posts = await storageService.getAllPosts();
      expect(posts.first.isLiked, true);
      expect(posts.first.likeCount, 1);
    });

    test('应该能够删除帖子', () async {
      // 创建测试帖子
      final testPost = CommunityPost(
        id: 1003,
        author: UserInfo(
          id: 1,
          username: '测试用户',
          avatar: 'test_avatar.jpg',
          isVerified: false,
        ),
        content: '待删除的帖子',
        type: PostType.study,
        tags: ['测试'],
        images: [],
        likeCount: 0,
        commentCount: 0,
        shareCount: 0,
        createdAt: DateTime.now(),
        isLiked: false,
      );

      // 添加帖子
      await storageService.addPost(testPost);
      expect((await storageService.getAllPosts()).length, 1);

      // 删除帖子
      final deleteResult = await storageService.deletePost(1003);
      expect(deleteResult, true);

      // 验证删除
      final posts = await storageService.getAllPosts();
      expect(posts.length, 0);
    });

    test('应该能够生成唯一的帖子ID', () async {
      final id1 = await storageService.getNextPostId();
      final id2 = await storageService.getNextPostId();

      expect(id1, isNot(equals(id2)));
      expect(id2, equals(id1 + 1));
    });

    tearDown(() async {
      // 清理测试数据
      await storageService.clearAllPosts();
    });
  });
}
