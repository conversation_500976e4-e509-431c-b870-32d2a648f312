import 'package:flutter/material.dart';
import '../lib/features/wage_system/services/premium_article_service.dart';

/// 优质文章访问权限功能测试
/// 
/// 测试完整的优质文章访问流程：
/// 1. 权限检查
/// 2. 权限激活
/// 3. 权限验证
/// 4. 权限过期处理
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🧪 开始测试优质文章访问权限功能');
  
  try {
    await testPremiumArticleAccessFlow();
    print('✅ 所有测试通过！');
  } catch (e) {
    print('❌ 测试失败: $e');
  }
}

/// 测试完整的优质文章访问流程
Future<void> testPremiumArticleAccessFlow() async {
  final service = PremiumArticleService.instance;
  await service.initialize();
  
  print('\n📋 测试步骤 1: 初始状态检查');
  
  // 重置权限状态
  await service.resetPremiumAccess();
  
  // 检查初始状态
  assert(!service.hasPremiumAccess, '初始状态应该没有优质文章访问权限');
  print('✓ 初始状态正确：没有优质文章访问权限');
  
  // 测试文章访问验证
  final accessResult1 = await service.validateArticleAccess('premium_article_1');
  assert(!accessResult1.isAllowed, '没有权限时应该拒绝访问优质文章');
  assert(accessResult1.deniedReason != null, '应该提供拒绝原因');
  print('✓ 权限验证正确：拒绝访问优质文章');
  
  // 测试普通文章访问
  final accessResult2 = await service.validateArticleAccess('normal_article_5');
  assert(accessResult2.isAllowed, '普通文章应该允许访问');
  print('✓ 普通文章访问正常');
  
  print('\n📋 测试步骤 2: 权限激活');
  
  // 激活优质文章访问权限（永久）
  final activationSuccess = await service.activatePremiumAccess(
    activationType: PremiumActivationType.purchase,
    itemId: '14',
    metadata: {
      'purchaseTime': DateTime.now().toIso8601String(),
      'price': 300.0,
    },
  );
  
  assert(activationSuccess, '权限激活应该成功');
  assert(service.hasPremiumAccess, '激活后应该有优质文章访问权限');
  print('✓ 权限激活成功');
  
  // 检查激活记录
  final activeRecord = service.getCurrentActiveRecord();
  assert(activeRecord != null, '应该有活跃的激活记录');
  assert(activeRecord!.isPermanent, '购买的权限应该是永久的');
  assert(activeRecord.activationType == PremiumActivationType.purchase, '激活类型应该是购买');
  print('✓ 激活记录正确');
  
  print('\n📋 测试步骤 3: 权限验证');
  
  // 测试优质文章访问
  final accessResult3 = await service.validateArticleAccess('premium_article_1');
  assert(accessResult3.isAllowed, '有权限时应该允许访问优质文章');
  assert(accessResult3.isPremium, '应该标记为优质文章访问');
  assert(accessResult3.activationRecord != null, '应该包含激活记录');
  print('✓ 优质文章访问验证成功');
  
  // 测试普通文章访问（仍然正常）
  final accessResult4 = await service.validateArticleAccess('normal_article_5');
  assert(accessResult4.isAllowed, '普通文章应该继续允许访问');
  assert(!accessResult4.isPremium, '普通文章不应该标记为优质');
  print('✓ 普通文章访问仍然正常');
  
  print('\n📋 测试步骤 4: 权限持久化');
  
  // 重新初始化服务（模拟应用重启）
  final newService = PremiumArticleService.instance;
  await newService.initialize();
  
  assert(newService.hasPremiumAccess, '重启后应该保持优质文章访问权限');
  print('✓ 权限持久化成功');
  
  // 检查激活历史
  final history = newService.activationHistory;
  assert(history.isNotEmpty, '应该有激活历史记录');
  assert(history.first.activationType == PremiumActivationType.purchase, '历史记录应该正确');
  print('✓ 激活历史记录正确');
  
  print('\n📋 测试步骤 5: 临时权限测试');
  
  // 重置并测试临时权限
  await service.resetPremiumAccess();
  
  // 激活临时权限（1小时）
  final tempActivationSuccess = await service.activatePremiumAccess(
    activationType: PremiumActivationType.trial,
    duration: const Duration(hours: 1),
    metadata: {'trial': true},
  );
  
  assert(tempActivationSuccess, '临时权限激活应该成功');
  assert(service.hasPremiumAccess, '激活后应该有权限');
  
  final tempRecord = service.getCurrentActiveRecord();
  assert(tempRecord != null, '应该有临时激活记录');
  assert(!tempRecord!.isPermanent, '试用权限不应该是永久的');
  assert(tempRecord.getRemainingTime() != null, '应该有剩余时间');
  print('✓ 临时权限激活成功');
  
  print('\n📋 测试步骤 6: 文章类型判断');
  
  // 测试文章类型判断逻辑
  assert(service.isArticlePremium('premium_article_1'), 'premium开头的文章应该是优质文章');
  assert(service.isArticlePremium('1_some_article'), '1开头的文章应该是优质文章');
  assert(service.isArticlePremium('2_another_article'), '2开头的文章应该是优质文章');
  assert(service.isArticlePremium('3_third_article'), '3开头的文章应该是优质文章');
  assert(!service.isArticlePremium('4_normal_article'), '4开头的文章不应该是优质文章');
  assert(!service.isArticlePremium('normal_article'), '普通文章不应该是优质文章');
  print('✓ 文章类型判断逻辑正确');
  
  print('\n📋 测试步骤 7: 权限状态更新');
  
  // 测试权限状态检查和更新
  await service.checkAndUpdatePermissionStatus();
  assert(service.hasPremiumAccess, '有效权限应该保持');
  print('✓ 权限状态更新正常');
  
  print('\n🎉 优质文章访问权限功能测试完成！');
  print('📊 测试统计:');
  print('   - 权限激活: ✓');
  print('   - 权限验证: ✓');
  print('   - 权限持久化: ✓');
  print('   - 临时权限: ✓');
  print('   - 文章类型判断: ✓');
  print('   - 权限状态更新: ✓');
}
