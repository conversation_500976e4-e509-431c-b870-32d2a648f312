# 个人资料编辑功能

## 功能概述

OneDay应用的个人资料编辑功能允许用户自定义个人信息，包括头像、昵称和个人简介。该功能遵循Notion风格设计，提供直观的用户体验。

## 核心特性

### 1. 头像管理
- **头像选择**：支持从相机拍摄或相册选择图片
- **图片裁剪**：自动裁剪为1:1比例的正方形头像
- **格式支持**：JPG、JPEG、PNG、WebP格式
- **文件大小限制**：最大5MB
- **本地存储**：头像文件保存在应用文档目录

### 2. 昵称编辑
- **长度限制**：1-20个字符
- **实时验证**：输入时即时验证格式
- **自动保存**：失去焦点时自动保存
- **默认值**：新用户默认昵称为"学习者"

### 3. 个人简介
- **长度限制**：最多100个字符
- **多行输入**：支持3行文本输入
- **可选字段**：可以为空
- **自动保存**：失去焦点时自动保存
- **默认值**：新用户默认简介为"让每一天都充满收获"

## 技术架构

### 数据模型

```dart
class UserProfile {
  final String userId;        // 用户ID
  final String nickname;      // 昵称
  final String? bio;          // 个人简介（可选）
  final String? avatarPath;   // 头像本地路径（可选）
  final DateTime createdAt;   // 创建时间
  final DateTime updatedAt;   // 更新时间
}
```

### 服务层

**UserProfileService** 负责数据持久化和业务逻辑：

- **数据存储**：使用SharedPreferences存储用户资料JSON
- **头像管理**：使用path_provider管理头像文件
- **数据验证**：验证输入格式和长度
- **错误处理**：完善的异常处理机制

### 状态管理

使用Riverpod进行状态管理：

```dart
// 主要Provider
final userProfileProvider = StateNotifierProvider<UserProfileNotifier, UserProfileState>

// 便捷访问Provider
final currentUserProfileProvider = Provider<UserProfile?>
final userProfileLoadingProvider = Provider<bool>
final userProfileUpdatingProvider = Provider<bool>
final userProfileErrorProvider = Provider<String?>
```

### UI组件

**ProfileEditPage** 主要功能：

- **头像编辑区域**：显示当前头像，提供更换和删除选项
- **基本信息编辑**：昵称和个人简介输入框
- **表单验证**：实时验证和错误提示
- **自动保存**：失去焦点时自动保存更改
- **加载状态**：显示保存进度和状态

## 用户交互流程

### 1. 编辑头像
1. 点击个人中心的编辑按钮
2. 进入编辑页面，点击头像区域
3. 选择拍照或从相册选择
4. 自动裁剪为正方形
5. 保存并更新显示

### 2. 编辑昵称
1. 在编辑页面点击昵称输入框
2. 输入新昵称（1-20字符）
3. 失去焦点时自动保存
4. 显示保存状态

### 3. 编辑个人简介
1. 在编辑页面点击个人简介输入框
2. 输入简介内容（最多100字符）
3. 失去焦点时自动保存
4. 可以清空设置为空

## 数据持久化

### 存储方式
- **用户资料**：SharedPreferences存储JSON数据
- **头像文件**：应用文档目录/avatars/文件夹
- **存储键**：`oneday_user_profile`

### 数据结构
```json
{
  "userId": "default_user",
  "nickname": "学习者",
  "bio": "让每一天都充满收获",
  "avatarPath": "/path/to/avatar.jpg",
  "createdAt": "2024-01-01T12:00:00.000Z",
  "updatedAt": "2024-01-02T15:30:00.000Z"
}
```

## 错误处理

### 输入验证
- **昵称为空**：提示"昵称不能为空"
- **昵称过长**：提示"昵称长度应在1-20个字符之间"
- **简介过长**：提示"个人简介长度不能超过100个字符"
- **图片格式错误**：提示"不支持的图片格式"
- **图片过大**：提示"图片文件过大，请选择小于5MB的图片"

### 网络和存储错误
- **保存失败**：显示具体错误信息
- **文件操作失败**：提示用户重试
- **权限不足**：引导用户授权

## 性能优化

### 图片处理
- **压缩质量**：85%质量压缩
- **尺寸限制**：最大1024x1024像素
- **格式转换**：自动选择最优格式

### 数据缓存
- **内存缓存**：Service层缓存当前用户资料
- **懒加载**：按需加载头像文件
- **状态同步**：Provider自动同步状态

## 测试覆盖

### 单元测试
- **模型测试**：UserProfile序列化/反序列化
- **服务测试**：UserProfileService所有方法
- **验证测试**：输入验证逻辑
- **边界测试**：极限值和特殊字符

### 集成测试
- **状态管理测试**：Provider状态变化
- **数据持久化测试**：存储和读取
- **错误处理测试**：异常情况处理

## 依赖包

```yaml
dependencies:
  image_picker: ^1.0.7      # 图片选择
  image_cropper: ^5.0.1     # 图片裁剪
  path_provider: ^2.1.5     # 文件路径
  shared_preferences: ^2.2.3 # 本地存储
  flutter_riverpod: ^2.4.9  # 状态管理
```

## 使用示例

### 基本用法
```dart
// 获取当前用户资料
final profile = ref.watch(currentUserProfileProvider);

// 更新昵称
await ref.read(userProfileProvider.notifier).updateNickname('新昵称');

// 更新头像
await ref.read(userProfileProvider.notifier).updateAvatar(imageFile);

// 监听错误
ref.listen(userProfileErrorProvider, (previous, next) {
  if (next != null) {
    // 显示错误提示
  }
});
```

### 导航到编辑页面
```dart
Navigator.of(context).push(
  MaterialPageRoute(
    builder: (context) => const ProfileEditPage(),
  ),
);
```

## 未来扩展

### 计划功能
- **云端同步**：支持多设备同步
- **头像模板**：提供默认头像选择
- **社交集成**：从社交平台导入头像
- **批量编辑**：支持批量修改多个字段
- **历史记录**：保存编辑历史

### 技术改进
- **图片优化**：WebP格式支持
- **离线支持**：离线编辑，联网时同步
- **性能监控**：编辑操作性能统计
- **A/B测试**：不同UI方案测试

## 注意事项

1. **权限管理**：确保相机和存储权限
2. **文件清理**：定期清理无用的头像文件
3. **数据备份**：重要数据的备份策略
4. **隐私保护**：用户数据的隐私保护
5. **兼容性**：不同设备和系统版本的兼容性
