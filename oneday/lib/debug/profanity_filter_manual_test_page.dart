import 'package:flutter/material.dart';
import '../features/community/article_import_service.dart';
import '../features/community/profanity_filter_service.dart';
import '../features/community/community_storage_service.dart';
import '../features/community/community_feed_page.dart';

/// 敏感词过滤手动测试页面
class ProfanityFilterManualTestPage extends StatefulWidget {
  const ProfanityFilterManualTestPage({super.key});

  @override
  State<ProfanityFilterManualTestPage> createState() =>
      _ProfanityFilterManualTestPageState();
}

class _ProfanityFilterManualTestPageState
    extends State<ProfanityFilterManualTestPage> {
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _contentController = TextEditingController();
  final ArticleImportService _importService = ArticleImportService();
  final ProfanityFilterService _filterService = ProfanityFilterService();
  final CommunityStorageService _storageService =
      CommunityStorageService.instance;

  String _testResult = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeServices();

    // 设置测试数据
    _titleController.text = '测试标题傻逼';
    _contentController.text = '这是测试内容操你妈的白痴';
  }

  Future<void> _initializeServices() async {
    await _filterService.initialize();
    await _storageService.initialize();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('敏感词过滤测试'),
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF37352F),
        elevation: 0,
        centerTitle: true,
      ),
      backgroundColor: Colors.white,
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '标题:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _titleController,
              decoration: InputDecoration(
                hintText: '输入包含敏感词的标题...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFF2F76DA)),
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              '内容:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _contentController,
              maxLines: 3,
              decoration: InputDecoration(
                hintText: '输入包含敏感词的内容...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFF2F76DA)),
                ),
              ),
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testProfanityFilter,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF2F76DA),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                        : const Text('测试敏感词过滤'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testFullPublishFlow,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0F7B6C),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('测试完整发布流程'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            const Text(
              '测试结果:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: const Color(0xFFE3E2E0)),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _testResult.isEmpty ? '暂无测试结果' : _testResult,
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testProfanityFilter() async {
    setState(() {
      _isLoading = true;
      _testResult = '';
    });

    try {
      final title = _titleController.text.trim();
      final content = _contentController.text.trim();

      final results = <String>[];
      results.add('=== 敏感词过滤测试 ===\n');

      // 测试标题
      results.add('📝 原始标题: "$title"');
      final titleResult = await _importService.processArticleContent(title);
      results.add('🔍 标题检测结果:');
      results.add('  - 是否有违禁词: ${titleResult.hasProfanityViolations}');
      results.add('  - 过滤词汇数量: ${titleResult.profanityWordsFiltered}');
      results.add(
        '  - 过滤后内容: "${_removeHtmlTags(titleResult.highlightedText)}"',
      );

      results.add('');

      // 测试内容
      results.add('📝 原始内容: "$content"');
      final contentResult = await _importService.processArticleContent(content);
      results.add('🔍 内容检测结果:');
      results.add('  - 是否有违禁词: ${contentResult.hasProfanityViolations}');
      results.add('  - 过滤词汇数量: ${contentResult.profanityWordsFiltered}');
      results.add(
        '  - 过滤后内容: "${_removeHtmlTags(contentResult.highlightedText)}"',
      );

      setState(() {
        _testResult = results.join('\n');
      });
    } catch (e) {
      setState(() {
        _testResult = '测试失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testFullPublishFlow() async {
    setState(() {
      _isLoading = true;
      _testResult = '';
    });

    try {
      final title = _titleController.text.trim();
      final content = _contentController.text.trim();

      final results = <String>[];
      results.add('=== 完整发布流程测试 ===\n');

      // 第一步：检测敏感词
      results.add('🔍 第一步：检测敏感词');
      final titleResult = await _importService.processArticleContent(title);
      final contentResult = await _importService.processArticleContent(content);

      // 获取过滤后的内容
      String filteredTitle = _removeHtmlTags(titleResult.highlightedText);
      String filteredContent = _removeHtmlTags(contentResult.highlightedText);

      results.add('📝 过滤后标题: "$filteredTitle"');
      results.add('📝 过滤后内容: "$filteredContent"');

      // 第二步：创建帖子
      results.add('\n💾 第二步：创建帖子');
      final postId = await _storageService.getNextPostId();
      final currentUser = UserInfo(
        id: 1,
        username: '测试用户',
        avatar: 'test_avatar.jpg',
        isVerified: false,
      );

      final newPost = CommunityPost(
        id: postId,
        author: currentUser,
        content: '${filteredTitle.trim()}\n\n${filteredContent.trim()}',
        type: PostType.study,
        tags: ['测试'],
        images: [],
        likeCount: 0,
        commentCount: 0,
        shareCount: 0,
        createdAt: DateTime.now(),
        isLiked: false,
        isPremium: false,
      );

      // 第三步：保存帖子
      results.add('\n💾 第三步：保存帖子');
      final saveSuccess = await _storageService.addPost(newPost);
      results.add('保存结果: ${saveSuccess ? "成功" : "失败"}');

      if (saveSuccess) {
        // 第四步：验证保存的内容
        results.add('\n✅ 第四步：验证保存的内容');
        final savedPosts = await _storageService.getAllPosts();
        final savedPost = savedPosts.firstWhere((p) => p.id == postId);

        results.add('保存的帖子内容: "${savedPost.content}"');
        results.add(
          '是否包含原始敏感词: ${savedPost.content.contains('傻逼') || savedPost.content.contains('操') ? "是" : "否"}',
        );
        results.add(
          '是否包含过滤标记: ${savedPost.content.contains('***') ? "是" : "否"}',
        );
      }

      setState(() {
        _testResult = results.join('\n');
      });
    } catch (e) {
      setState(() {
        _testResult = '测试失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 移除HTML标签
  String _removeHtmlTags(String htmlString) {
    if (htmlString.isEmpty) return htmlString;

    final RegExp htmlTagRegExp = RegExp(r'<[^>]*>');
    return htmlString.replaceAll(htmlTagRegExp, '');
  }
}
