/// 道具效果类型枚举
enum ItemEffectType {
  /// 学习时长倍数增强
  studyTimeMultiplier,

  /// 记忆效果增强
  memoryBoost,

  /// 答题正确率提升
  accuracyBoost,

  /// 创作效率提升
  creativityBoost,

  /// 语言学习效率提升
  languageBoost,

  /// 专注力提升
  focusBoost,

  /// 防打扰模式
  doNotDisturb,

  /// 时间暂停功能
  timePause,

  /// 解锁内容访问
  contentAccess,

  /// AI助手功能
  aiAssistant,

  /// 记忆场景解锁
  memoryScenes,

  /// AI联想提示
  aiAssociation,
}

/// 活跃道具效果数据模型
class ActiveItemEffect {
  final String id;
  final String itemId;
  final String itemName;
  final ItemEffectType effectType;
  final double effectValue; // 效果数值（如倍数、百分比等）
  final DateTime activatedAt;
  final DateTime? expiresAt;
  final bool isPermanent;
  final Map<String, dynamic> additionalData;

  ActiveItemEffect({
    required this.id,
    required this.itemId,
    required this.itemName,
    required this.effectType,
    required this.effectValue,
    required this.activatedAt,
    this.expiresAt,
    this.isPermanent = false,
    this.additionalData = const {},
  });

  /// 检查效果是否已过期
  bool get isExpired {
    if (isPermanent) return false;
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// 获取剩余时间
  Duration? get remainingTime {
    if (isPermanent || expiresAt == null) return null;
    final now = DateTime.now();
    if (now.isAfter(expiresAt!)) return Duration.zero;
    return expiresAt!.difference(now);
  }

  /// 从JSON创建实例
  factory ActiveItemEffect.fromJson(Map<String, dynamic> json) {
    return ActiveItemEffect(
      id: json['id'] as String,
      itemId: json['itemId'] as String,
      itemName: json['itemName'] as String,
      effectType: ItemEffectType.values.firstWhere(
        (e) => e.name == json['effectType'],
        orElse: () => ItemEffectType.studyTimeMultiplier,
      ),
      effectValue: (json['effectValue'] as num).toDouble(),
      activatedAt: DateTime.parse(json['activatedAt'] as String),
      expiresAt: json['expiresAt'] != null
          ? DateTime.parse(json['expiresAt'] as String)
          : null,
      isPermanent: json['isPermanent'] as bool? ?? false,
      additionalData: Map<String, dynamic>.from(json['additionalData'] ?? {}),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'itemId': itemId,
      'itemName': itemName,
      'effectType': effectType.name,
      'effectValue': effectValue,
      'activatedAt': activatedAt.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
      'isPermanent': isPermanent,
      'additionalData': additionalData,
    };
  }

  /// 复制并修改部分属性
  ActiveItemEffect copyWith({
    String? id,
    String? itemId,
    String? itemName,
    ItemEffectType? effectType,
    double? effectValue,
    DateTime? activatedAt,
    DateTime? expiresAt,
    bool? isPermanent,
    Map<String, dynamic>? additionalData,
  }) {
    return ActiveItemEffect(
      id: id ?? this.id,
      itemId: itemId ?? this.itemId,
      itemName: itemName ?? this.itemName,
      effectType: effectType ?? this.effectType,
      effectValue: effectValue ?? this.effectValue,
      activatedAt: activatedAt ?? this.activatedAt,
      expiresAt: expiresAt ?? this.expiresAt,
      isPermanent: isPermanent ?? this.isPermanent,
      additionalData: additionalData ?? this.additionalData,
    );
  }

  @override
  String toString() {
    return 'ActiveItemEffect(id: $id, itemName: $itemName, effectType: $effectType, effectValue: $effectValue, remainingTime: $remainingTime)';
  }
}

/// 道具使用结果
class ItemUsageResult {
  final bool success;
  final String message;
  final ActiveItemEffect? effect;
  final String? errorCode;

  ItemUsageResult({
    required this.success,
    required this.message,
    this.effect,
    this.errorCode,
  });

  factory ItemUsageResult.success(String message, {ActiveItemEffect? effect}) {
    return ItemUsageResult(success: true, message: message, effect: effect);
  }

  factory ItemUsageResult.failure(String message, {String? errorCode}) {
    return ItemUsageResult(
      success: false,
      message: message,
      errorCode: errorCode,
    );
  }
}

/// 道具效果配置
class ItemEffectConfig {
  static ItemEffectType? getEffectType(String itemId) {
    switch (itemId) {
      case '1': // 专注药水
        return ItemEffectType.studyTimeMultiplier;
      case '2': // 时间暂停器
        return ItemEffectType.timePause;
      case '3': // 防打扰护盾
        return ItemEffectType.doNotDisturb;
      case '4': // 记忆增强剂
        return ItemEffectType.memoryBoost;
      case '5': // 宫殿建造包
        return ItemEffectType.memoryScenes;
      case '6': // 联想连接器
        return ItemEffectType.aiAssociation;
      case '7': // 答题加速器
        return ItemEffectType.accuracyBoost;
      case '8': // 灵感之泉
        return ItemEffectType.creativityBoost;
      case '9': // 语言天赋
        return ItemEffectType.languageBoost;
      case '12': // 优质文章阅读券
        return ItemEffectType.contentAccess;
      case '13': // AI学习伙伴
        return ItemEffectType.aiAssistant;
      default:
        return null;
    }
  }

  static double getEffectValue(String itemId) {
    switch (itemId) {
      case '1': // 专注药水 - 学习时长翻倍
        return 2.0;
      case '2': // 时间暂停器 - 5分钟暂停时间
        return 5.0;
      case '3': // 防打扰护盾 - 屏蔽通知
        return 1.0;
      case '4': // 记忆增强剂 - 记忆效果翻倍
        return 2.0;
      case '5': // 宫殿建造包 - 解锁10个场景
        return 10.0;
      case '6': // 联想连接器 - AI联想功能
        return 1.0;
      case '7': // 答题加速器 - 正确率+20%
        return 0.2;
      case '8': // 灵感之泉 - 创作效率提升
        return 1.5;
      case '9': // 语言天赋 - 语言学习效率翻倍
        return 2.0;
      case '12': // 优质文章阅读券 - 访问权限
        return 1.0;
      case '13': // AI学习伙伴 - AI助手功能
        return 1.0;
      default:
        return 1.0;
    }
  }

  static String getEffectDescription(ItemEffectType effectType, double value) {
    switch (effectType) {
      case ItemEffectType.studyTimeMultiplier:
        return '学习时长计算 ×${value.toStringAsFixed(1)}';
      case ItemEffectType.memoryBoost:
        return '记忆效果 ×${value.toStringAsFixed(1)}';
      case ItemEffectType.accuracyBoost:
        return '答题正确率 +${(value * 100).toStringAsFixed(0)}%';
      case ItemEffectType.creativityBoost:
        return '创作效率 ×${value.toStringAsFixed(1)}';
      case ItemEffectType.languageBoost:
        return '语言学习效率 ×${value.toStringAsFixed(1)}';
      case ItemEffectType.focusBoost:
        return '专注力大幅提升';
      case ItemEffectType.doNotDisturb:
        return '防打扰模式已激活';
      case ItemEffectType.timePause:
        return '可暂停计时 ${value.toStringAsFixed(0)} 分钟';
      case ItemEffectType.contentAccess:
        return '优质内容访问权限';
      case ItemEffectType.aiAssistant:
        return 'AI学习助手已激活';
      case ItemEffectType.memoryScenes:
        return '解锁 ${value.toStringAsFixed(0)} 个记忆场景';
      case ItemEffectType.aiAssociation:
        return 'AI联想提示功能';
    }
  }
}
