import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/item_effect_service.dart';
import '../models/item_effect_models.dart';

/// 道具效果服务提供者
final itemEffectServiceProvider = Provider<ItemEffectService>((ref) {
  return ItemEffectService();
});

/// 活跃效果列表提供者
final activeEffectsProvider = StreamProvider<List<ActiveItemEffect>>((ref) {
  final service = ref.watch(itemEffectServiceProvider);
  
  // 创建一个流来监听效果变化
  return Stream.periodic(const Duration(seconds: 30), (count) {
    return service.activeEffects;
  }).asyncMap((effects) async {
    return effects;
  });
});

/// 用户背包提供者
final userInventoryProvider = StreamProvider<Map<String, int>>((ref) {
  final service = ref.watch(itemEffectServiceProvider);
  
  return Stream.periodic(const Duration(seconds: 10), (count) {
    return service.userInventory;
  }).asyncMap((inventory) async {
    return inventory;
  });
});

/// 学习时长倍数提供者
final studyTimeMultiplierProvider = Provider<double>((ref) {
  final service = ref.watch(itemEffectServiceProvider);
  return service.getStudyTimeMultiplier();
});

/// 记忆效果倍数提供者
final memoryBoostMultiplierProvider = Provider<double>((ref) {
  final service = ref.watch(itemEffectServiceProvider);
  return service.getMemoryBoostMultiplier();
});

/// 答题正确率加成提供者
final accuracyBoostProvider = Provider<double>((ref) {
  final service = ref.watch(itemEffectServiceProvider);
  return service.getAccuracyBoost();
});

/// 特定效果类型检查提供者
final hasActiveEffectProvider = Provider.family<bool, ItemEffectType>((ref, effectType) {
  final service = ref.watch(itemEffectServiceProvider);
  return service.hasActiveEffect(effectType);
});

/// 获取特定效果提供者
final getActiveEffectProvider = Provider.family<ActiveItemEffect?, ItemEffectType>((ref, effectType) {
  final service = ref.watch(itemEffectServiceProvider);
  return service.getActiveEffect(effectType);
});
