import 'package:flutter/material.dart';
import 'item_effect_service.dart';
import '../models/item_effect_models.dart';
import '../../time_box/models/timebox_models.dart';

/// 道具与TimeBox系统集成服务
///
/// 负责将道具效果应用到学习任务中
class ItemTimeBoxIntegrationService {
  static final ItemTimeBoxIntegrationService _instance =
      ItemTimeBoxIntegrationService._internal();
  factory ItemTimeBoxIntegrationService() => _instance;
  ItemTimeBoxIntegrationService._internal();

  final ItemEffectService _itemEffectService = ItemEffectService();

  /// 计算带有道具效果的学习时长
  ///
  /// [originalMinutes] 原始学习时长（分钟）
  /// 返回应用道具效果后的实际学习时长
  int calculateEffectiveStudyTime(int originalMinutes) {
    final multiplier = _itemEffectService.getStudyTimeMultiplier();
    final effectiveTime = (originalMinutes * multiplier).round();

    debugPrint(
      '🎯 学习时长计算: 原始$originalMinutes分钟 × $multiplier倍 = $effectiveTime分钟',
    );
    return effectiveTime;
  }

  /// 计算带有道具效果的记忆效率
  ///
  /// [baseEfficiency] 基础记忆效率
  /// 返回应用道具效果后的记忆效率
  double calculateMemoryEfficiency(double baseEfficiency) {
    final multiplier = _itemEffectService.getMemoryBoostMultiplier();
    final effectiveEfficiency = baseEfficiency * multiplier;

    debugPrint(
      '🧠 记忆效率计算: 基础$baseEfficiency × $multiplier倍 = $effectiveEfficiency',
    );
    return effectiveEfficiency;
  }

  /// 计算带有道具效果的答题正确率
  ///
  /// [baseAccuracy] 基础正确率 (0.0 - 1.0)
  /// 返回应用道具效果后的正确率
  double calculateAccuracy(double baseAccuracy) {
    final boost = _itemEffectService.getAccuracyBoost();
    final effectiveAccuracy = (baseAccuracy + boost).clamp(0.0, 1.0);

    debugPrint(
      '🎯 答题正确率计算: 基础${(baseAccuracy * 100).toStringAsFixed(1)}% + ${(boost * 100).toStringAsFixed(1)}% = ${(effectiveAccuracy * 100).toStringAsFixed(1)}%',
    );
    return effectiveAccuracy;
  }

  /// 检查是否有防打扰效果激活
  bool get isDoNotDisturbActive {
    return _itemEffectService.hasActiveEffect(ItemEffectType.doNotDisturb);
  }

  /// 检查是否有时间暂停功能可用
  bool get isTimePauseAvailable {
    return _itemEffectService.hasActiveEffect(ItemEffectType.timePause);
  }

  /// 检查是否有AI助手功能可用
  bool get isAIAssistantAvailable {
    return _itemEffectService.hasActiveEffect(ItemEffectType.aiAssistant);
  }

  /// 检查是否有优质内容访问权限
  bool get hasContentAccess {
    return _itemEffectService.hasActiveEffect(ItemEffectType.contentAccess);
  }

  /// 获取当前激活的效果列表
  List<ActiveItemEffect> get activeEffects {
    return _itemEffectService.activeEffects;
  }

  /// 获取效果描述文本
  List<String> getActiveEffectDescriptions() {
    return activeEffects.map((effect) {
      return ItemEffectConfig.getEffectDescription(
        effect.effectType,
        effect.effectValue,
      );
    }).toList();
  }

  /// 为任务应用道具效果
  ///
  /// [task] 原始任务
  /// 返回应用效果后的任务信息（用于显示）
  Map<String, dynamic> applyEffectsToTask(TimeBoxTask task) {
    final originalMinutes = task.plannedMinutes;
    final effectiveMinutes = calculateEffectiveStudyTime(originalMinutes);

    final effects = getActiveEffectDescriptions();

    return {
      'originalMinutes': originalMinutes,
      'effectiveMinutes': effectiveMinutes,
      'studyTimeMultiplier': _itemEffectService.getStudyTimeMultiplier(),
      'memoryMultiplier': _itemEffectService.getMemoryBoostMultiplier(),
      'accuracyBoost': _itemEffectService.getAccuracyBoost(),
      'activeEffects': effects,
      'hasDoNotDisturb': isDoNotDisturbActive,
      'hasTimePause': isTimePauseAvailable,
      'hasAIAssistant': isAIAssistantAvailable,
      'hasContentAccess': hasContentAccess,
    };
  }

  /// 获取任务完成时的奖励倍数
  ///
  /// 基于激活的道具效果计算额外奖励
  double getCompletionRewardMultiplier() {
    double multiplier = 1.0;

    // 专注药水提供额外奖励
    if (_itemEffectService.hasActiveEffect(
      ItemEffectType.studyTimeMultiplier,
    )) {
      multiplier += 0.5; // 额外50%奖励
    }

    // 记忆增强剂提供额外奖励
    if (_itemEffectService.hasActiveEffect(ItemEffectType.memoryBoost)) {
      multiplier += 0.3; // 额外30%奖励
    }

    // 答题加速器提供额外奖励
    if (_itemEffectService.hasActiveEffect(ItemEffectType.accuracyBoost)) {
      multiplier += 0.2; // 额外20%奖励
    }

    debugPrint('🎁 任务完成奖励倍数: ${multiplier}x');
    return multiplier;
  }

  /// 获取学习会话的效果摘要
  ///
  /// 用于在学习完成后显示道具效果的贡献
  Map<String, dynamic> getSessionEffectSummary(
    int originalMinutes,
    int actualMinutes,
  ) {
    final studyMultiplier = _itemEffectService.getStudyTimeMultiplier();
    final memoryMultiplier = _itemEffectService.getMemoryBoostMultiplier();
    final accuracyBoost = _itemEffectService.getAccuracyBoost();
    final rewardMultiplier = getCompletionRewardMultiplier();

    final timeBonus = actualMinutes - originalMinutes;
    final effectiveTimeBonus = studyMultiplier > 1.0 ? timeBonus : 0;

    return {
      'originalMinutes': originalMinutes,
      'actualMinutes': actualMinutes,
      'timeBonus': effectiveTimeBonus,
      'studyMultiplier': studyMultiplier,
      'memoryMultiplier': memoryMultiplier,
      'accuracyBoost': accuracyBoost,
      'rewardMultiplier': rewardMultiplier,
      'activeEffectCount': activeEffects.length,
      'effectDescriptions': getActiveEffectDescriptions(),
    };
  }

  /// 检查特定类型的道具是否可以使用
  ///
  /// [effectType] 效果类型
  /// 返回是否可以使用该类型的道具
  bool canUseItemType(ItemEffectType effectType) {
    // 检查是否已有相同类型的效果激活
    return !_itemEffectService.hasActiveEffect(effectType);
  }

  /// 获取推荐使用的道具类型
  ///
  /// 基于当前学习状态推荐合适的道具
  List<ItemEffectType> getRecommendedItemTypes({
    required String categoryName,
    required int plannedMinutes,
    required TaskPriority priority,
  }) {
    final recommendations = <ItemEffectType>[];

    // 基于任务类别推荐
    switch (categoryName) {
      case '计算机科学':
      case '数学':
        if (canUseItemType(ItemEffectType.accuracyBoost)) {
          recommendations.add(ItemEffectType.accuracyBoost);
        }
        break;
      case '英语':
        if (canUseItemType(ItemEffectType.languageBoost)) {
          recommendations.add(ItemEffectType.languageBoost);
        }
        if (canUseItemType(ItemEffectType.memoryBoost)) {
          recommendations.add(ItemEffectType.memoryBoost);
        }
        break;
      case '政治':
        if (canUseItemType(ItemEffectType.memoryBoost)) {
          recommendations.add(ItemEffectType.memoryBoost);
        }
        break;
      default:
        // 其他类别的通用推荐
        break;
    }

    // 基于任务时长推荐
    if (plannedMinutes >= 60) {
      if (canUseItemType(ItemEffectType.studyTimeMultiplier)) {
        recommendations.add(ItemEffectType.studyTimeMultiplier);
      }
      if (canUseItemType(ItemEffectType.doNotDisturb)) {
        recommendations.add(ItemEffectType.doNotDisturb);
      }
    }

    // 基于优先级推荐
    if (priority == TaskPriority.high) {
      if (canUseItemType(ItemEffectType.focusBoost)) {
        recommendations.add(ItemEffectType.focusBoost);
      }
    }

    return recommendations;
  }

  /// 格式化效果持续时间
  String formatEffectDuration(ActiveItemEffect effect) {
    if (effect.isPermanent) {
      return '永久';
    }

    final remaining = effect.remainingTime;
    if (remaining == null) {
      return '未知';
    }

    if (remaining.inHours > 0) {
      return '${remaining.inHours}小时${remaining.inMinutes.remainder(60)}分钟';
    } else if (remaining.inMinutes > 0) {
      return '${remaining.inMinutes}分钟';
    } else {
      return '${remaining.inSeconds}秒';
    }
  }
}
