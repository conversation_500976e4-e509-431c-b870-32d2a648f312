import 'dart:io';
import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../shared/utils/ui_utils.dart';
import 'content_report_dialog.dart';
import 'community_storage_service.dart';
import '../wage_system/services/premium_article_service.dart';

/// 社区动态页面 - 学习社交与内容分享平台
class CommunityFeedPage extends StatefulWidget {
  const CommunityFeedPage({super.key});

  @override
  State<CommunityFeedPage> createState() => _CommunityFeedPageState();
}

class _CommunityFeedPageState extends State<CommunityFeedPage>
    with WidgetsBindingObserver, RouteAware {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  final CommunityStorageService _storageService =
      CommunityStorageService.instance;

  // 状态管理
  List<CommunityPost> _posts = [];
  List<CommunityPost> _filteredPosts = [];
  bool _isLoading = false;
  bool _hasMore = true;
  int _currentPage = 1;
  DateTime? _lastRefreshTime;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializePosts();
    _scrollController.addListener(_onScroll);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 注册路由监听器
    final route = ModalRoute.of(context);
    if (route is PageRoute) {
      // 这里我们简化处理，直接在每次依赖变化时检查刷新
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // 强制刷新数据，不受时间限制
        _forceRefreshData();
      });
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  void didPopNext() {
    // 当从其他页面返回到此页面时
    debugPrint('🔄 返回社区页面，强制刷新数据');
    _forceRefreshData();
  }

  /// 强制刷新数据（不受时间限制）
  void _forceRefreshData() async {
    debugPrint('🔄 强制刷新社区数据');
    _lastRefreshTime = DateTime.now();
    // 强制刷新缓存并重新加载数据
    _storageService.refreshCache();
    await _loadRealPosts();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // 当应用从后台返回前台时，刷新数据
    if (state == AppLifecycleState.resumed) {
      _refreshDataIfNeeded();
    }
  }

  /// 检查是否需要刷新数据
  void _refreshDataIfNeeded() async {
    // 如果距离上次刷新超过5分钟，或者从未刷新过，则自动刷新
    final now = DateTime.now();
    if (_lastRefreshTime == null ||
        now.difference(_lastRefreshTime!).inMinutes > 5) {
      debugPrint('🔄 检测到需要刷新社区数据');
      _lastRefreshTime = now;
      // 不清空缓存，直接重新加载
      await _loadRealPosts();
    }
  }

  /// 初始化社区动态数据
  void _initializePosts() async {
    await _storageService.initialize();
    await _loadRealPosts();
    _lastRefreshTime = DateTime.now();
  }

  /// 加载真实帖子数据
  Future<void> _loadRealPosts() async {
    try {
      // 获取存储的帖子
      final storedPosts = await _storageService.getAllPosts();

      debugPrint('📱 加载到 ${storedPosts.length} 个帖子');

      // 检查是否需要初始化模拟数据
      // 只有在完全没有数据且是首次启动时才生成模拟数据
      if (storedPosts.isEmpty && !await _hasEverHadPosts()) {
        debugPrint('🎭 首次启动，生成模拟数据作为示例');
        final mockPosts = _generateMockPosts();
        for (final post in mockPosts) {
          await _storageService.addPost(post);
        }
        // 标记已经生成过模拟数据
        await _markHasHadPosts();

        // 重新获取数据
        final allPosts = await _storageService.getAllPosts();
        setState(() {
          _posts = allPosts;
          _filteredPosts = List.from(_posts);
        });
      } else {
        setState(() {
          _posts = storedPosts;
          _filteredPosts = List.from(_posts);
        });
      }

      _applyFilters();
    } catch (e) {
      debugPrint('❌ 加载帖子数据失败: $e');
      // 如果加载失败，显示空状态而不是生成模拟数据
      setState(() {
        _posts = [];
        _filteredPosts = [];
      });
    }
  }

  /// 检查是否曾经有过帖子数据
  Future<bool> _hasEverHadPosts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('community_has_had_posts') ?? false;
    } catch (e) {
      return false;
    }
  }

  /// 标记已经有过帖子数据
  Future<void> _markHasHadPosts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('community_has_had_posts', true);
    } catch (e) {
      debugPrint('❌ 标记帖子状态失败: $e');
    }
  }

  /// 滚动监听 - 无限加载
  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        _hasMore) {
      _loadMorePosts();
    }
  }

  /// 下拉刷新
  Future<void> _handleRefresh() async {
    try {
      debugPrint('🔄 手动刷新社区数据');

      // 强制刷新缓存并重新加载数据
      _storageService.refreshCache();
      await _loadRealPosts();
      _lastRefreshTime = DateTime.now();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('动态已更新 (${_posts.length} 条)'),
            backgroundColor: const Color(0xFF2E7EED),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ 刷新失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('刷新失败，请重试'),
            duration: Duration(seconds: 1),
            backgroundColor: Color(0xFFE03E3E),
          ),
        );
      }
    }
  }

  /// 加载更多动态
  Future<void> _loadMorePosts() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    await Future.delayed(const Duration(seconds: 1));

    final newPosts = _generateMockPosts(page: _currentPage + 1);

    setState(() {
      _posts.addAll(newPosts);
      _currentPage++;
      _isLoading = false;
      _hasMore = newPosts.isNotEmpty;
    });

    _applyFilters();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F6F3),
      appBar: _buildAppBar(),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// 构建顶部应用栏
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      title: const Text(
        '学习社区',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: Color(0xFF37352F),
        ),
      ),
      actions: [
        IconButton(
          onPressed: () async {
            debugPrint('🔄 手动刷新社区数据');
            await _handleRefresh();
          },
          icon: const Icon(Icons.refresh, color: Color(0xFF787774)),
          tooltip: '刷新',
        ),
        IconButton(
          onPressed: () {
            UIUtils.showComingSoonSnackBar(context);
          },
          icon: const Icon(Icons.tune, color: Color(0xFF787774)),
          tooltip: '筛选和排序',
        ),
        IconButton(
          onPressed: () {
            UIUtils.showComingSoonSnackBar(context);
          },
          icon: const Icon(
            Icons.notifications_outlined,
            color: Color(0xFF787774),
          ),
          tooltip: '消息通知',
        ),
      ],
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(60),
        child: _buildSearchBar(),
      ),
    );
  }

  /// 构建搜索栏
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
      color: Colors.white,
      child: TextField(
        controller: _searchController,
        onChanged: (_) => _applyFilters(),
        style: const TextStyle(fontSize: 14, color: Color(0xFF37352F)),
        decoration: InputDecoration(
          hintText: '搜索用户、内容或话题...',
          hintStyle: const TextStyle(fontSize: 14, color: Color(0xFF9B9A97)),
          filled: true,
          fillColor: Colors.white,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Color(0xFFE3E2DE), width: 1),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Color(0xFFE3E2DE), width: 1),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Color(0xFF2E7EED), width: 2),
          ),
          prefixIcon: const Icon(
            Icons.search,
            color: Color(0xFF9B9A97),
            size: 20,
          ),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    _searchController.clear();
                    _applyFilters();
                  },
                  icon: const Icon(
                    Icons.clear,
                    color: Color(0xFF9B9A97),
                    size: 18,
                  ),
                )
              : null,
        ),
      ),
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    if (_filteredPosts.isEmpty && !_isLoading) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _handleRefresh,
      color: const Color(0xFF2E7EED),
      child: CustomScrollView(
        controller: _scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        slivers: [
          SliverPadding(
            padding: const EdgeInsets.all(16),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  if (index >= _filteredPosts.length) {
                    return _buildLoadingIndicator();
                  }
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: CommunityPostCard(
                      post: _filteredPosts[index],
                      onLike: () => _toggleLike(_filteredPosts[index].id),
                      onComment: () => _showComments(_filteredPosts[index]),
                      onShare: () => _sharePost(_filteredPosts[index]),
                      onReport: () => _reportPost(_filteredPosts[index]),
                      onTap: () => _handlePostTap(_filteredPosts[index]),
                    ),
                  );
                },
                childCount:
                    _filteredPosts.length + (_hasMore && _isLoading ? 1 : 0),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.forum_outlined,
              size: 40,
              color: Color(0xFF2E7EED),
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            '暂无动态',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '成为第一个分享学习心得的人吧',
            style: TextStyle(fontSize: 14, color: Color(0xFF787774)),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () async {
              final result = await context.push('/community-post-editor');
              // 如果发布成功，刷新数据
              if (result == true) {
                debugPrint('📱 发布成功，刷新社区动态列表');
                await _handleRefresh();
              }
            },
            icon: const Icon(Icons.edit),
            label: const Text('发布动态'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2E7EED),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建加载指示器
  Widget _buildLoadingIndicator() {
    return const Padding(
      padding: EdgeInsets.symmetric(vertical: 16),
      child: Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF2E7EED)),
        ),
      ),
    );
  }

  /// 构建浮动操作按钮
  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      heroTag: "community_fab", // 唯一标签避免冲突
      onPressed: () async {
        final result = await context.push('/community-post-editor');
        // 如果发布成功，刷新数据
        if (result == true) {
          debugPrint('📱 发布成功，刷新社区动态列表');
          await _handleRefresh();
        }
      },
      backgroundColor: const Color(0xFF2E7EED),
      foregroundColor: Colors.white,
      child: const Icon(Icons.edit),
    );
  }

  /// 搜索和筛选
  void _applyFilters() {
    List<CommunityPost> filtered = List.from(_posts);

    // 搜索过滤
    if (_searchController.text.isNotEmpty) {
      final query = _searchController.text.toLowerCase();
      filtered = filtered
          .where(
            (post) =>
                post.content.toLowerCase().contains(query) ||
                post.author.username.toLowerCase().contains(query) ||
                post.tags.any((tag) => tag.toLowerCase().contains(query)),
          )
          .toList();
    }

    setState(() {
      _filteredPosts = filtered;
    });
  }

  /// 点赞动态
  void _toggleLike(int postId) async {
    setState(() {
      final postIndex = _posts.indexWhere((p) => p.id == postId);
      if (postIndex != -1) {
        _posts[postIndex].isLiked = !_posts[postIndex].isLiked;
        _posts[postIndex].likeCount += _posts[postIndex].isLiked ? 1 : -1;
      }
    });
    _applyFilters();

    // 保存更新到存储
    try {
      final postIndex = _posts.indexWhere((p) => p.id == postId);
      if (postIndex != -1) {
        await _storageService.updatePost(_posts[postIndex]);
      }
    } catch (e) {
      debugPrint('❌ 保存点赞状态失败: $e');
    }
  }

  /// 分享动态
  void _sharePost(CommunityPost post) {
    Share.share(
      '${post.author.username}分享了学习心得：\n${post.content}\n\n来自OneDay学习社区',
      subject: '学习分享 - OneDay',
    );
  }

  /// 举报动态
  void _reportPost(CommunityPost post) {
    showContentReportDialog(
      context: context,
      contentId: post.id.toString(),
      contentType: 'post',
      reporterId: 'current_user_id', // 实际应用中应该从用户状态获取
      onReported: () {
        // 举报成功后的处理，可以考虑隐藏该动态或标记
        debugPrint('动态 ${post.id} 已被举报');
      },
    );
  }

  /// 处理帖子点击
  void _handlePostTap(CommunityPost post) async {
    if (post.isPremium) {
      // 检查优质文章访问权限
      final premiumService = PremiumArticleService.instance;
      final accessResult = await premiumService.validateArticleAccess(
        post.id.toString(),
      );

      if (!accessResult.isAllowed) {
        // 显示权限不足对话框
        _showPremiumAccessDialog(post);
        return;
      }
    }

    // 如果有权限或不是优质文章，显示文章详情
    _showPostDetail(post);
  }

  /// 显示优质文章权限对话框
  void _showPremiumAccessDialog(CommunityPost post) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: Row(
          children: [
            Icon(
              Icons.workspace_premium,
              color: const Color(0xFF7C3AED),
              size: 24,
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                '优质文章',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF37352F),
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '此文章需要优质文章访问权限才能查看完整内容。',
              style: TextStyle(fontSize: 14, color: Color(0xFF787774)),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFF7C3AED).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: const Color(0xFF7C3AED).withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '优质文章访问权限包含：',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF7C3AED),
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• 永久解锁所有优质文章\n• 享受深度学习内容和经验分享\n• 获取专业学习指导和技巧\n• 优先访问最新优质内容',
                    style: TextStyle(
                      fontSize: 11,
                      color: Color(0xFF787774),
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消', style: TextStyle(color: Color(0xFF9B9A97))),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // 导航到商城购买权限
              context.go('/store');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF7C3AED),
              foregroundColor: Colors.white,
            ),
            child: const Text('去购买'),
          ),
        ],
      ),
    );
  }

  /// 显示帖子详情
  void _showPostDetail(CommunityPost post) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Container(
          constraints: const BoxConstraints(maxHeight: 600),
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 头部
              Row(
                children: [
                  CircleAvatar(
                    radius: 16,
                    backgroundImage: NetworkImage(post.author.avatar),
                    backgroundColor: const Color(0xFFF0F0F0),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          post.author.username,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF37352F),
                          ),
                        ),
                        Text(
                          _formatTimeStatic(post.createdAt),
                          style: const TextStyle(
                            fontSize: 12,
                            color: Color(0xFF9B9A97),
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (post.isPremium)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFF7C3AED).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: const Text(
                        '优质',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF7C3AED),
                        ),
                      ),
                    ),
                  const SizedBox(width: 8),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                    color: const Color(0xFF787774),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // 内容
              Flexible(
                child: SingleChildScrollView(
                  child: Text(
                    post.content,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF37352F),
                      height: 1.5,
                    ),
                  ),
                ),
              ),

              // 标签
              if (post.tags.isNotEmpty) ...[
                const SizedBox(height: 16),
                Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: post.tags.map((tag) {
                    return Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        '#$tag',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF2E7EED),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// 显示评论弹窗
  void _showComments(CommunityPost post) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Color(0xFFE3E2DE), width: 1),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '评论 (${post.commentCount})',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF37352F),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                    color: const Color(0xFF787774),
                  ),
                ],
              ),
            ),
            const Expanded(
              child: Center(
                child: Text(
                  '评论功能开发中...',
                  style: TextStyle(fontSize: 14, color: Color(0xFF787774)),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 格式化时间显示
  static String _formatTimeStatic(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${time.month}月${time.day}日';
    }
  }

  /// 生成模拟数据
  List<CommunityPost> _generateMockPosts({int page = 1}) {
    final baseIndex = (page - 1) * 10;

    return List.generate(10, (index) {
      final postIndex = baseIndex + index;
      final users = [
        UserInfo(
          id: 1,
          username: '考研小王',
          avatar:
              'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
          isVerified: true,
        ),
        UserInfo(
          id: 2,
          username: '学霸小李',
          avatar:
              'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150',
          isVerified: false,
        ),
        UserInfo(
          id: 3,
          username: '努力的小张',
          avatar:
              'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
          isVerified: true,
        ),
        UserInfo(
          id: 4,
          username: '编程达人',
          avatar:
              'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
          isVerified: false,
        ),
      ];

      final contents = [
        '今天刷了50道算法题，感觉对动态规划有了更深的理解！分享一个小技巧：先画状态转移图，再写代码会清晰很多。',
        '考研英语阅读提升心得：每天坚持精读一篇文章，标记生词并整理长难句。一个月下来效果显著！',
        '时间管理分享：使用番茄工作法学习，25分钟专注+5分钟休息，效率提升明显。配合OneDay的时间盒子功能超棒！',
        '记忆宫殿学习法真的很有效！把政治知识点放在熟悉的场景中，记忆效果比死记硬背好太多了。',
        '刚完成今天的学习计划，累计学习8小时，赚了1600虚拟工资😄 坚持就是胜利！',
        '分享一个数学复习技巧：错题本不只是抄题目，更要分析错误原因和解题思路。',
        '今天在图书馆学习时遇到了志同道合的小伙伴，一起讨论问题的感觉真好！学习不是孤独的战斗。',
        '熬夜学习效率真的不高，调整作息后状态好了很多。早睡早起身体好，学习效率也更高！',
      ];

      final postTypes = [
        PostType.study,
        PostType.experience,
        PostType.question,
        PostType.achievement,
      ];
      final tags = [
        ['考研', '算法'],
        ['英语', '阅读技巧'],
        ['时间管理', '效率提升'],
        ['记忆法', '政治'],
        ['学习打卡', '成就'],
        ['数学', '复习方法'],
        ['学习氛围', '图书馆'],
        ['作息调整', '健康学习'],
      ];

      return CommunityPost(
        id: postIndex + 1,
        author: users[postIndex % users.length],
        content: contents[postIndex % contents.length],
        type: postTypes[postIndex % postTypes.length],
        tags: tags[postIndex % tags.length],
        images: postIndex % 3 == 0
            ? [
                'https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=600',
              ]
            : [],
        likeCount: 15 + (postIndex % 50),
        commentCount: 3 + (postIndex % 10),
        shareCount: 1 + (postIndex % 5),
        createdAt: DateTime.now().subtract(Duration(hours: postIndex * 2)),
        isLiked: postIndex % 4 == 0,
        isPremium: postIndex % 5 == 0, // 每5个帖子中有1个是优质文章
      );
    });
  }
}

/// 社区动态卡片组件
class CommunityPostCard extends StatelessWidget {
  final CommunityPost post;
  final VoidCallback onLike;
  final VoidCallback onComment;
  final VoidCallback onShare;
  final VoidCallback? onReport;
  final VoidCallback? onTap;

  const CommunityPostCard({
    super.key,
    required this.post,
    required this.onLike,
    required this.onComment,
    required this.onShare,
    this.onReport,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: post.isPremium
              ? Border.all(
                  color: const Color(0xFF7C3AED).withValues(alpha: 0.3),
                  width: 1.5,
                )
              : null,
          boxShadow: [
            BoxShadow(
              color: post.isPremium
                  ? const Color(0xFF7C3AED).withValues(alpha: 0.1)
                  : Colors.black.withValues(alpha: 0.04),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildUserHeader(),
            const SizedBox(height: 12),
            _buildContent(),
            if (post.images.isNotEmpty) ...[
              const SizedBox(height: 12),
              _buildImages(),
            ],
            if (post.tags.isNotEmpty) ...[
              const SizedBox(height: 12),
              _buildTags(),
            ],
            const SizedBox(height: 12),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildUserHeader() {
    return Row(
      children: [
        CircleAvatar(
          radius: 20,
          backgroundImage: NetworkImage(post.author.avatar),
          backgroundColor: const Color(0xFFF0F0F0),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    post.author.username,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF37352F),
                    ),
                  ),
                  if (post.author.isVerified) ...[
                    const SizedBox(width: 4),
                    const Icon(
                      Icons.verified,
                      size: 16,
                      color: Color(0xFF2E7EED),
                    ),
                  ],
                ],
              ),
              const SizedBox(height: 2),
              Text(
                _formatTime(post.createdAt),
                style: const TextStyle(fontSize: 12, color: Color(0xFF9B9A97)),
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: _getTypeColor(post.type).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            _getTypeName(post.type),
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: _getTypeColor(post.type),
            ),
          ),
        ),
        if (post.isPremium) ...[
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: const Color(0xFF7C3AED).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.workspace_premium,
                  size: 10,
                  color: const Color(0xFF7C3AED),
                ),
                const SizedBox(width: 2),
                const Text(
                  '优质',
                  style: TextStyle(
                    fontSize: 9,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF7C3AED),
                  ),
                ),
              ],
            ),
          ),
        ],
        if (onReport != null) ...[
          const SizedBox(width: 8),
          InkWell(
            onTap: onReport,
            borderRadius: BorderRadius.circular(16),
            child: Container(
              padding: const EdgeInsets.all(4),
              child: const Icon(
                Icons.more_vert,
                size: 16,
                color: Color(0xFF9B9A97),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildContent() {
    return Text(
      post.content,
      style: const TextStyle(
        fontSize: 14,
        height: 1.5,
        color: Color(0xFF37352F),
      ),
    );
  }

  Widget _buildImages() {
    final imagePath = post.images.first;

    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: _buildImageWidget(imagePath),
    );
  }

  /// 构建图片组件，支持网络URL和本地文件路径
  Widget _buildImageWidget(String imagePath) {
    // 判断是否为网络URL
    final isNetworkUrl =
        imagePath.startsWith('http://') || imagePath.startsWith('https://');

    if (isNetworkUrl) {
      // 网络图片
      return Image.network(
        imagePath,
        width: double.infinity,
        height: 200,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildErrorPlaceholder();
        },
      );
    } else {
      // 本地文件
      final file = File(imagePath);
      return FutureBuilder<bool>(
        future: file.exists(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // 加载中
            return Container(
              width: double.infinity,
              height: 200,
              color: const Color(0xFFF0F0F0),
              child: const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF2E7EED)),
                ),
              ),
            );
          }

          if (snapshot.data == true) {
            // 文件存在，显示图片
            return Image.file(
              file,
              width: double.infinity,
              height: 200,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                print('❌ 加载本地图片失败: $imagePath, 错误: $error');
                return _buildErrorPlaceholder();
              },
            );
          } else {
            // 文件不存在
            print('❌ 本地图片文件不存在: $imagePath');
            return _buildErrorPlaceholder();
          }
        },
      );
    }
  }

  /// 构建错误占位符
  Widget _buildErrorPlaceholder() {
    return Container(
      width: double.infinity,
      height: 200,
      color: const Color(0xFFF0F0F0),
      child: const Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.image_not_supported, color: Color(0xFF9B9A97), size: 48),
          SizedBox(height: 8),
          Text(
            '图片加载失败',
            style: TextStyle(color: Color(0xFF9B9A97), fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildTags() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: post.tags
          .map(
            (tag) => Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '#$tag',
                style: const TextStyle(
                  fontSize: 12,
                  color: Color(0xFF2E7EED),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          )
          .toList(),
    );
  }

  Widget _buildActions() {
    return Row(
      children: [
        _buildActionButton(
          icon: post.isLiked ? Icons.favorite : Icons.favorite_border,
          label: post.likeCount.toString(),
          color: post.isLiked ? Colors.red : const Color(0xFF787774),
          onTap: onLike,
        ),
        const SizedBox(width: 24),
        _buildActionButton(
          icon: Icons.chat_bubble_outline,
          label: post.commentCount.toString(),
          color: const Color(0xFF787774),
          onTap: onComment,
        ),
        const SizedBox(width: 24),
        _buildActionButton(
          icon: Icons.share_outlined,
          label: post.shareCount.toString(),
          color: const Color(0xFF787774),
          onTap: onShare,
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(4),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 18, color: color),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${time.month}月${time.day}日';
    }
  }

  Color _getTypeColor(PostType type) {
    switch (type) {
      case PostType.study:
        return const Color(0xFF2E7EED);
      case PostType.experience:
        return const Color(0xFF0F7B6C);
      case PostType.question:
        return const Color(0xFFD9730D);
      case PostType.achievement:
        return const Color(0xFF7C3AED);
      default:
        return const Color(0xFF787774);
    }
  }

  String _getTypeName(PostType type) {
    switch (type) {
      case PostType.study:
        return '学习心得';
      case PostType.experience:
        return '经验分享';
      case PostType.question:
        return '提问求助';
      case PostType.achievement:
        return '成果展示';
      default:
        return '全部';
    }
  }
}

/// 数据模型

/// 社区动态
class CommunityPost {
  final int id;
  final UserInfo author;
  final String content;
  final PostType type;
  final List<String> tags;
  final List<String> images;
  int likeCount;
  final int commentCount;
  final int shareCount;
  final DateTime createdAt;
  bool isLiked;
  final bool isPremium; // 是否为优质文章

  CommunityPost({
    required this.id,
    required this.author,
    required this.content,
    required this.type,
    required this.tags,
    required this.images,
    required this.likeCount,
    required this.commentCount,
    required this.shareCount,
    required this.createdAt,
    this.isLiked = false,
    this.isPremium = false,
  });

  /// 从 JSON 创建对象
  factory CommunityPost.fromJson(Map<String, dynamic> json) {
    return CommunityPost(
      id: json['id'] as int,
      author: UserInfo.fromJson(json['author'] as Map<String, dynamic>),
      content: json['content'] as String,
      type: PostType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => PostType.study,
      ),
      tags: List<String>.from(json['tags'] as List),
      images: List<String>.from(json['images'] as List),
      likeCount: json['likeCount'] as int,
      commentCount: json['commentCount'] as int,
      shareCount: json['shareCount'] as int,
      createdAt: DateTime.parse(json['createdAt'] as String),
      isLiked: json['isLiked'] as bool? ?? false,
      isPremium: json['isPremium'] as bool? ?? false,
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'author': author.toJson(),
      'content': content,
      'type': type.name,
      'tags': tags,
      'images': images,
      'likeCount': likeCount,
      'commentCount': commentCount,
      'shareCount': shareCount,
      'createdAt': createdAt.toIso8601String(),
      'isLiked': isLiked,
      'isPremium': isPremium,
    };
  }
}

/// 用户信息
class UserInfo {
  final int id;
  final String username;
  final String avatar;
  final bool isVerified;

  UserInfo({
    required this.id,
    required this.username,
    required this.avatar,
    this.isVerified = false,
  });

  /// 从 JSON 创建对象
  factory UserInfo.fromJson(Map<String, dynamic> json) {
    return UserInfo(
      id: json['id'] as int,
      username: json['username'] as String,
      avatar: json['avatar'] as String,
      isVerified: json['isVerified'] as bool? ?? false,
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'avatar': avatar,
      'isVerified': isVerified,
    };
  }
}

/// 动态类型
enum PostType {
  all, // 全部
  study, // 学习心得
  experience, // 经验分享
  question, // 提问求助
  achievement, // 成果展示
}

/// 排序方式
enum SortOrder {
  latest, // 最新发布
  popular, // 最受欢迎
}
