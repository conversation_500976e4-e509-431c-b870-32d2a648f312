import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

// 导入社区相关服务
import '../community/community_storage_service.dart';
import '../community/community_feed_page.dart';

// 导入运动界面和数据
import 'exercise_session_page.dart';
import '../../core/data/pao_exercises_data.dart';
import '../vocabulary/word_meaning_service.dart';
import '../vocabulary/vocabulary_service.dart';
import 'custom_exercise_category.dart';
import 'custom_action_library_service.dart';
import 'custom_action_library.dart';
import 'create_custom_library_dialog.dart';
import 'manage_custom_libraries_dialog.dart';
import 'action_library_category_manager.dart';
import 'add_to_memory_dialog.dart';

// 动作库管理页面 - 专为考研考公考编人群设计的动觉记忆系统
// 提供浏览所有动作、按分类筛选、个人收藏等功能
class ExerciseLibraryPage extends ConsumerStatefulWidget {
  const ExerciseLibraryPage({super.key});

  @override
  ConsumerState<ExerciseLibraryPage> createState() =>
      _ExerciseLibraryPageState();
}

class _ExerciseLibraryPageState extends ConsumerState<ExerciseLibraryPage> {
  // 搜索控制器
  final TextEditingController _searchController = TextEditingController();

  // 状态管理
  final String _selectedScenario = '全部'; // 场景筛选：全部/简单活动/集中训练
  String _selectedCategory = '全部'; // 分类筛选：全部/健身/球类/养生等
  final String _selectedLetter = '全部'; // 字母筛选：全部/A-Z
  List<PAOExercise> _filteredExercises = []; // 过滤后的动作列表
  final Set<String> _favoriteExercises = {}; // 收藏的动作列表（使用字母标识）

  // 抽屉状态管理
  bool _isSidebarVisible = false;

  // 自定义分类管理器
  final CustomExerciseCategoryManager _customCategoryManager =
      CustomExerciseCategoryManager();

  // 自定义动作库服务
  final CustomActionLibraryService _customLibraryService =
      CustomActionLibraryService();

  // 树形分类管理器
  final ActionLibraryCategoryManager _treeCategoryManager =
      ActionLibraryCategoryManager();

  @override
  void initState() {
    super.initState();
    _initializeData();
    _loadCustomCategories();
    _loadCustomLibraries();
    _initializeTreeCategories();
    _loadFavoriteExercises();
    _searchController.addListener(_filterExercises);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  // 初始化数据
  void _initializeData() {
    _filteredExercises = PAOExercisesData.getAllExercises().values.toList();
  }

  // 加载自定义分类
  Future<void> _loadCustomCategories() async {
    await _customCategoryManager.loadFromStorage();
    if (mounted) {
      setState(() {
        // 重新过滤动作，包含自定义分类的动作
        _filterExercises();
      });
    }
  }

  // 加载自定义动作库
  Future<void> _loadCustomLibraries() async {
    await _customLibraryService.loadFromStorage();
    if (mounted) {
      setState(() {
        // 如果需要在界面上显示自定义动作库信息，可以在这里更新状态
      });
    }
  }

  // 初始化树形分类
  void _initializeTreeCategories() {
    _initializeTreeCategoriesWithExistingData();
    print('📂 动作库分类数据已重新组织为树形结构');
    print('🔧 分类管理器初始化完成，分类数量: ${_treeCategoryManager.categories.length}');
    for (final category in _treeCategoryManager.categories) {
      print('📂 分类: ${category.title} (子分类: ${category.children.length})');
    }
  }

  // 使用现有数据初始化树形分类
  void _initializeTreeCategoriesWithExistingData() {
    // 使用公共方法初始化默认分类
    _treeCategoryManager.initializeDefaultCategories();

    // 添加任何未归类的现有分类
    _addUnclassifiedCategories();
  }

  // 添加未归类的现有分类
  void _addUnclassifiedCategories() {
    final existingCategories = PAOExercisesData.allCategories.keys.toSet();
    final classifiedCategories = {'健身', '篮球', '足球', '瑜伽', '养生', '护眼', '拉伸'};

    final unclassifiedCategories = existingCategories.difference(
      classifiedCategories,
    );

    for (final categoryName in unclassifiedCategories) {
      // 将未归类的分类添加为根级分类
      _treeCategoryManager.addNode(categoryName);
      print('📂 添加未归类分类: $categoryName');
    }
  }

  // 过滤动作列表
  void _filterExercises() {
    final query = _searchController.text.toLowerCase();

    setState(() {
      // 获取所有动作：内置动作 + 自定义分类中的动作 + 自定义动作库中的动作
      final allExercises = <PAOExercise>[];

      // 检查是否选择了自定义动作库
      final selectedLibrary = _getSelectedCustomLibrary();

      if (selectedLibrary != null) {
        // 如果选择了自定义动作库，只显示该动作库中的动作
        allExercises.addAll(_getCustomLibraryExercises(selectedLibrary));
        print(
          '📚 显示自定义动作库"${selectedLibrary.name}"中的动作: ${allExercises.length}个',
        );
      } else {
        // 否则显示所有动作
        // 添加内置动作
        allExercises.addAll(PAOExercisesData.getAllExercises().values);

        // 添加自定义分类中的动作
        for (final customCategory in _customCategoryManager.categories) {
          allExercises.addAll(customCategory.exercises.values);
        }

        // 如果选择"全部"，还要添加所有自定义动作库中的动作
        if (_selectedCategory == '全部') {
          for (final library in _customLibraryService.libraries) {
            allExercises.addAll(_getCustomLibraryExercises(library));
          }
        }
      }

      _filteredExercises = allExercises.where((exercise) {
        // 搜索过滤
        final matchesSearch =
            exercise.name.toLowerCase().contains(query) ||
            exercise.nameEn.toLowerCase().contains(query) ||
            exercise.description.toLowerCase().contains(query);

        // 场景过滤
        final matchesScenario =
            _selectedScenario == '全部' ||
            (_selectedScenario == '我的收藏'
                ? _isFavorite(exercise.letter)
                : exercise.scenarios.contains(_selectedScenario));

        // 分类过滤（如果选择了自定义动作库，跳过分类过滤）
        final matchesCategory =
            selectedLibrary != null ||
            _selectedCategory == '全部' ||
            exercise.category == _selectedCategory;

        // 字母过滤
        final matchesLetter =
            _selectedLetter == '全部' || exercise.letter == _selectedLetter;

        return matchesSearch &&
            matchesScenario &&
            matchesCategory &&
            matchesLetter;
      }).toList();
    });
  }

  /// 获取当前选择的自定义动作库
  CustomActionLibrary? _getSelectedCustomLibrary() {
    // 检查当前选择的分类是否是自定义动作库的名称
    for (final library in _customLibraryService.libraries) {
      if (library.name == _selectedCategory) {
        return library;
      }
    }
    return null;
  }

  /// 将自定义动作库中的动作转换为PAOExercise格式
  List<PAOExercise> _getCustomLibraryExercises(CustomActionLibrary library) {
    final exercises = <PAOExercise>[];

    for (final entry in library.actions.entries) {
      final letter = entry.key;
      final action = entry.value;

      // 将自定义动作转换为PAOExercise格式
      final exercise = PAOExercise(
        letter: letter,
        nameCn: action.nameCn, // 修复：使用正确的属性名
        nameEn: action.nameEn.isNotEmpty ? action.nameEn : action.nameCn,
        description: action.description,
        category: library.category, // 使用动作库的分类
        scene: '自定义动作库', // 标记为自定义动作库
        keywords: action.keywords.isNotEmpty
            ? action
                  .keywords // 使用自定义的关键词
            : [action.nameEn, action.nameCn, letter], // 如果没有关键词，使用名称作为关键词
      );

      exercises.add(exercise);
    }

    return exercises;
  }

  // 抽屉控制方法
  void _toggleSidebar() {
    setState(() {
      _isSidebarVisible = !_isSidebarVisible;
    });
  }

  // 加载收藏的动作
  Future<void> _loadFavoriteExercises() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoriteList = prefs.getStringList('favorite_exercises') ?? [];
      setState(() {
        _favoriteExercises.clear();
        _favoriteExercises.addAll(favoriteList);
      });
    } catch (e) {
      debugPrint('加载收藏动作失败: $e');
    }
  }

  // 保存收藏的动作到本地存储
  Future<void> _saveFavoriteExercises() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList(
        'favorite_exercises',
        _favoriteExercises.toList(),
      );
    } catch (e) {
      debugPrint('保存收藏动作失败: $e');
    }
  }

  // 收藏功能
  void _toggleFavorite(String exerciseLetter) {
    setState(() {
      if (_favoriteExercises.contains(exerciseLetter)) {
        _favoriteExercises.remove(exerciseLetter);
      } else {
        _favoriteExercises.add(exerciseLetter);
      }
    });
    // 保存到本地存储
    _saveFavoriteExercises();
  }

  bool _isFavorite(String exerciseLetter) {
    return _favoriteExercises.contains(exerciseLetter);
  }

  void _onCategorySelected(String category) async {
    setState(() {
      _selectedCategory = category;
      _isSidebarVisible = false; // 选择后自动关闭抽屉
    });

    // 如果选择的是自定义动作库，重新加载数据以确保显示最新内容
    final selectedLibrary = _customLibraryService.libraries
        .where((lib) => lib.name == category)
        .firstOrNull;
    if (selectedLibrary != null) {
      await _customLibraryService.loadFromStorage();
      print('🔄 重新加载自定义动作库数据: ${selectedLibrary.name}');
    }

    _filterExercises();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F6F3), // Notion风格浅灰背景
      appBar: _buildAppBar(),
      body: Stack(
        children: [
          // 主内容区域
          _filteredExercises.isEmpty
              ? const Center(child: Text('暂无匹配的动作'))
              : Column(
                  children: [
                    // 编辑功能提示 - 优化空间利用率
                    Container(
                      margin: const EdgeInsets.fromLTRB(
                        12,
                        12,
                        12,
                        8,
                      ), // 优化边距，减少垂直空间
                      padding: const EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 8,
                      ), // 减少内边距
                      decoration: BoxDecoration(
                        color: const Color(
                          0xFF2E7EED,
                        ).withValues(alpha: 0.08), // 降低背景透明度
                        borderRadius: BorderRadius.circular(6), // 从8减少到6
                        border: Border.all(
                          color: const Color(
                            0xFF2E7EED,
                          ).withValues(alpha: 0.15),
                        ), // 降低边框透明度
                      ),
                      child: const Row(
                        children: [
                          Icon(
                            Icons.edit,
                            size: 14,
                            color: Color(0xFF2E7EED),
                          ), // 从16减少到14
                          SizedBox(width: 6), // 从8减少到6
                          Expanded(
                            child: Text(
                              '💡 长按动作卡片或点击详情页的编辑按钮即可自定义动作内容',
                              style: TextStyle(
                                fontSize: 11, // 从12减少到11
                                color: Color(0xFF2E7EED),
                                fontWeight: FontWeight.w500,
                                height: 1.3, // 添加行高控制
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    // 动作网格
                    Expanded(child: _buildExerciseGrid()),
                  ],
                ),

          // 抽屉组件
          ActionLibrarySidebar(
            isVisible: _isSidebarVisible,
            onClose: _toggleSidebar,
            onCategorySelected: _onCategorySelected,
            selectedCategory: _selectedCategory,
            searchController: _searchController,
            onSearchChanged: _filterExercises,
            customCategoryManager: _customCategoryManager,
            treeCategoryManager: _treeCategoryManager,
            customLibraryService: _customLibraryService,
            onCategoriesChanged: () {
              setState(() {
                _filterExercises();
              });
            },
          ),
        ],
      ),
      floatingActionButton: _buildAnimatedFloatingActionButton(),
    );
  }

  // 构建应用栏
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        _selectedCategory == '全部' ? '动作库管理' : _selectedCategory,
        style: const TextStyle(
          color: Color(0xFF37352F),
          fontWeight: FontWeight.w600,
        ),
      ),
      backgroundColor: Colors.white,
      elevation: 0,
      iconTheme: const IconThemeData(color: Color(0xFF37352F)),
      leading: IconButton(
        icon: const Icon(Icons.menu, color: Color(0xFF37352F)),
        onPressed: _toggleSidebar,
        tooltip: '打开分类',
      ),
      actions: [
        // 帮助按钮
        IconButton(
          icon: const Icon(Icons.help_outline, color: Color(0xFF37352F)),
          onPressed: _showHelpDialog,
          tooltip: '动觉记忆法说明',
        ),
        // 更多选项 - Notion风格
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: Color(0xFF37352F)),
          onSelected: _handleMenuAction,
          color: Colors.white,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide(
              color: const Color(0xFF37352F).withValues(alpha: 0.1),
            ),
          ),
          itemBuilder: (context) => [
            _buildPopupMenuItem(
              value: 'create_custom_library',
              icon: Icons.library_add_outlined,
              text: '创建自定义动作库',
              color: const Color(0xFFE03E3E),
            ),
            _buildPopupMenuItem(
              value: 'manage_custom_libraries',
              icon: Icons.folder_outlined,
              text: '管理自定义动作库',
              color: const Color(0xFF7C3AED),
            ),
            const PopupMenuDivider(),
            _buildPopupMenuItem(
              value: 'word_practice',
              icon: Icons.quiz_outlined,
              text: '单词练习',
              color: const Color(0xFF2E7EED),
            ),
            _buildPopupMenuItem(
              value: 'add_to_memory',
              icon: Icons.bookmark_add_outlined,
              text: '添加到记忆',
              color: const Color(0xFF7C3AED),
            ),
            _buildPopupMenuItem(
              value: 'custom_word',
              icon: Icons.settings_outlined,
              text: '管理词汇',
              color: const Color(0xFF787774),
            ),
            const PopupMenuDivider(),
            _buildPopupMenuItem(
              value: 'import_library',
              icon: Icons.upload_outlined,
              text: '导入动作库',
              color: const Color(0xFF2E7EED),
            ),
            _buildPopupMenuItem(
              value: 'export_library',
              icon: Icons.download_outlined,
              text: '导出动作库',
              color: const Color(0xFF0F7B6C),
            ),
          ],
        ),
      ],
    );
  }

  // 构建自定义样式的PopupMenu项
  PopupMenuItem<String> _buildPopupMenuItem({
    required String value,
    required IconData icon,
    required String text,
    required Color color,
  }) {
    return PopupMenuItem<String>(
      value: value,
      child: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 12),
          Text(
            text,
            style: const TextStyle(color: Color(0xFF37352F), fontSize: 14),
          ),
        ],
      ),
    );
  }

  // 构建动作网格 - 优化布局密度和空间利用率
  Widget _buildExerciseGrid() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12), // 从16减少到12，增加可用空间
      child: LayoutBuilder(
        builder: (context, constraints) {
          // 根据可用宽度动态计算列数，优化空间利用率
          int columns = 2;
          double aspectRatio = 0.85; // 从0.75增加到0.85，减小卡片高度
          double spacing = 8; // 减少间距，提高空间利用率

          if (constraints.maxWidth > 900) {
            columns = 4;
            aspectRatio = 0.9; // 从0.8增加到0.9，减小卡片高度
            spacing = 10;
          } else if (constraints.maxWidth > 600) {
            columns = 3;
            aspectRatio = 0.88; // 从0.78增加到0.88，减小卡片高度
            spacing = 9;
          }

          // 平台特定调整
          if (Theme.of(context).platform == TargetPlatform.android) {
            aspectRatio -= 0.02; // Android设备稍微降低宽高比
          }

          return GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: columns,
              childAspectRatio: aspectRatio,
              crossAxisSpacing: spacing,
              mainAxisSpacing: spacing,
            ),
            itemCount: _filteredExercises.length,
            itemBuilder: (context, index) {
              return _buildExerciseCard(_filteredExercises[index]);
            },
          );
        },
      ),
    );
  }

  // 构建动作卡片 - 优化内容布局，确保不溢出
  Widget _buildExerciseCard(PAOExercise exercise) {
    return Semantics(
      label:
          '${exercise.letter}字母动作：${exercise.name}，适用场景：${exercise.scenarios.join('、')}',
      button: true,
      child: Card(
        elevation: 2,
        color: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => _showExerciseDetail(exercise),
          onLongPress: () => _showEditExerciseDialog(exercise),
          child: Padding(
            padding: const EdgeInsets.all(4), // 从6减少到4，进一步减小卡片高度
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 顶部区域：字母和场景标识 - 优化高度和字体
                SizedBox(
                  height: 20, // 从22减少到20，进一步减小高度
                  child: Row(
                    children: [
                      Container(
                        width: 20, // 从22减少到20，匹配新的高度
                        height: 20,
                        decoration: BoxDecoration(
                          color: _getLetterColor(
                            exercise.letter,
                          ).withValues(alpha: 0.12),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: Center(
                          child: Text(
                            exercise.letter,
                            style: TextStyle(
                              fontSize: 14, // 从11增加到14，提高可读性
                              fontWeight: FontWeight.w700, // 增强字重保持清晰度
                              color: _getLetterColor(exercise.letter),
                            ),
                          ),
                        ),
                      ),
                      const Spacer(),
                      // 收藏按钮 - 优化尺寸
                      GestureDetector(
                        onTap: () => _toggleFavorite(exercise.letter),
                        child: Icon(
                          _isFavorite(exercise.letter)
                              ? Icons.favorite
                              : Icons.favorite_border,
                          size: 14, // 从16减少到14
                          color: _isFavorite(exercise.letter)
                              ? Colors.red
                              : const Color(0xFF9B9A97),
                        ),
                      ),
                      const SizedBox(width: 3), // 从4减少到3
                      // 场景标签 - 优化字体和间距
                      ...exercise.scenarios
                          .take(2)
                          .map(
                            (scenario) => Container(
                              margin: const EdgeInsets.only(
                                left: 1.5,
                              ), // 从2减少到1.5
                              padding: const EdgeInsets.symmetric(
                                horizontal: 2.5,
                                vertical: 0.5,
                              ), // 进一步减少padding
                              decoration: BoxDecoration(
                                color: scenario == '简单活动'
                                    ? const Color(
                                        0xFF0F7B6C,
                                      ).withValues(alpha: 0.12)
                                    : const Color(
                                        0xFFE03E3E,
                                      ).withValues(alpha: 0.12),
                                borderRadius: BorderRadius.circular(2),
                              ),
                              child: Text(
                                scenario == '简单活动' ? '简' : '训',
                                style: TextStyle(
                                  fontSize: 9, // 从6.5增加到9，提高可读性
                                  color: scenario == '简单活动'
                                      ? const Color(0xFF0F7B6C)
                                      : const Color(0xFFE03E3E),
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                    ],
                  ),
                ),

                const SizedBox(height: 2), // 从4减少到2，进一步减小间距
                // 中间区域：动作名称 - 优化字体层级和间距
                Expanded(
                  flex: 3,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        exercise.name,
                        style: const TextStyle(
                          fontSize: 15, // 从13增加到15，提高主标题可读性
                          fontWeight: FontWeight.w700, // 从w600增加到w700，增强层级
                          color: Color(0xFF37352F),
                          height: 1.15, // 从1.1增加到1.15，改善可读性
                          letterSpacing: -0.2, // 添加字母间距优化
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 1), // 从2减少到1，减小间距
                      Text(
                        exercise.nameEn,
                        style: const TextStyle(
                          fontSize: 12, // 从10增加到12，保持可读性
                          color: Color(0xFF787774), // 从9B9A97调整为787774，增强对比度
                          height: 1.2, // 从1.1增加到1.2
                          fontWeight: FontWeight.w500, // 添加字重，提升清晰度
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),

                // 底部区域：分类和PAO信息 - 优化布局和字体
                SizedBox(
                  height: 26, // 从30减少到26，进一步减小高度
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      // 分类标签和PAO信息合并显示，提高空间利用率
                      Row(
                        children: [
                          // 分类标签
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 5,
                              vertical: 1.5,
                            ), // 优化padding
                            decoration: BoxDecoration(
                              color: _getCategoryColor(
                                exercise.category,
                              ).withValues(alpha: 0.12),
                              borderRadius: BorderRadius.circular(3),
                            ),
                            child: Text(
                              exercise.category,
                              style: TextStyle(
                                fontSize: 11, // 从9增加到11，保持可读性
                                color: _getCategoryColor(exercise.category),
                                fontWeight: FontWeight.w600, // 从w500增加到w600
                              ),
                            ),
                          ),

                          // 动觉记忆信息 - 与分类标签同行显示
                          if (exercise.paoWords.isNotEmpty) ...[
                            const SizedBox(width: 6), // 分类和PAO信息间距
                            Expanded(
                              child: Row(
                                children: [
                                  const Icon(
                                    Icons.psychology,
                                    size: 9,
                                    color: Color(0xFF2E7EED),
                                  ), // 从8增加到9
                                  const SizedBox(width: 3), // 从2增加到3
                                  Text(
                                    '${exercise.paoWords.length}词',
                                    style: const TextStyle(
                                      fontSize: 10, // 从8增加到10
                                      color: Color(0xFF2E7EED),
                                      fontWeight:
                                          FontWeight.w600, // 从w500增加到w600
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ],
                      ),

                      const SizedBox(height: 4), // 添加底部间距，改善视觉平衡
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 构建带动画的浮动操作按钮
  Widget _buildAnimatedFloatingActionButton() {
    return AnimatedSlide(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      offset: _isSidebarVisible ? const Offset(0, 2) : Offset.zero,
      child: AnimatedOpacity(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        opacity: _isSidebarVisible ? 0.0 : 1.0,
        child: FloatingActionButton.extended(
          onPressed: _isSidebarVisible ? null : _showCreateCustomLibraryDialog,
          backgroundColor: const Color(0xFF2E7EED),
          foregroundColor: Colors.white,
          elevation: 3,
          icon: const Icon(Icons.library_add),
          label: const Text('创建动作库'),
          tooltip: '创建自定义动作库',
          heroTag: "createActionLibraryButton", // 避免hero动画冲突
        ),
      ),
    );
  }

  // 显示动作详情
  void _showExerciseDetail(PAOExercise exercise) {
    showDialog(
      context: context,
      builder: (context) => PAOExerciseDetailDialog(exercise: exercise),
    );
  }

  // 显示编辑动作对话框
  void _showEditExerciseDialog(PAOExercise exercise) {
    showDialog(
      context: context,
      builder: (context) => EditExerciseDialog(exercise: exercise),
    ).then((result) {
      if (result != null && result is PAOExercise) {
        // 这里可以实现数据保存逻辑，目前只显示提示
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('动作"${result.name}"已更新（当前为演示模式）'),
              backgroundColor: const Color(0xFF2E7EED),
            ),
          );
        }
        setState(() {
          // 刷新界面以显示更新后的数据
          _initializeData();
        });
      }
    });
  }

  // 显示帮助对话框
  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('动觉记忆法说明'),
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '动觉记忆法Kinesthetic memory是一种学习与记忆策略，它强调通过身体体验、运动和触觉来理解和巩固信息。其核心理念是让学习者在学习过程中积极地调动身体，利用触觉、运动技能和身体感觉来有效吸收和保留知识。',
                style: TextStyle(fontSize: 14, height: 1.5),
              ),
              SizedBox(height: 12),
              Text(
                '💡 提示：运动时大脑会产生BDNF，有助于记忆巩固！',
                style: TextStyle(
                  color: Color(0xFF2E7EED),
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('知道了'),
          ),
        ],
      ),
    );
  }

  // 处理菜单动作
  void _handleMenuAction(String action) {
    switch (action) {
      case 'create_custom_library':
        _showCreateCustomLibraryDialog();
        break;
      case 'manage_custom_libraries':
        _showManageCustomLibrariesDialog();
        break;
      case 'word_practice':
        _startWordPractice();
        break;
      case 'add_to_memory':
        _showAddToMemoryDialog();
        break;
      case 'custom_word':
        _showCustomWordDialog();
        break;
      case 'import_library':
        _showImportLibraryDialog();
        break;
      case 'export_library':
        _exportLibrary();
        break;
    }
  }

  // 显示创建自定义动作库对话框
  void _showCreateCustomLibraryDialog() {
    showDialog(
      context: context,
      builder: (context) => CreateCustomLibraryDialog(
        customLibraryService: _customLibraryService,
        onLibraryCreated: (library) {
          setState(() {
            // 刷新界面
          });

          // 显示成功提示
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('自定义动作库"${library.name}"创建成功'),
              backgroundColor: const Color(0xFF0F7B6C),
              duration: const Duration(seconds: 2),
            ),
          );

          // 直接导航到编辑界面
          context
              .push(
                '/custom-library-editor',
                extra: {
                  'library': library,
                  'customLibraryService': _customLibraryService,
                },
              )
              .then((_) {
                // 从编辑界面返回后刷新当前界面
                setState(() {});
              });
        },
      ),
    );
  }

  // 显示管理自定义动作库对话框
  void _showManageCustomLibrariesDialog() {
    showDialog(
      context: context,
      builder: (context) => ManageCustomLibrariesDialog(
        customLibraryService: _customLibraryService,
        onLibrariesChanged: () {
          setState(() {
            // 刷新界面
          });
        },
      ),
    );
  }

  // 显示导入动作库对话框
  void _showImportLibraryDialog() {
    final textController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.upload,
                      color: Color(0xFF2E7EED),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '导入动作库',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF37352F),
                          ),
                        ),
                        Text(
                          '从JSON数据导入自定义动作库',
                          style: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF9B9A97),
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Color(0xFF9B9A97)),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // 输入框
              const Text(
                'JSON数据',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF37352F),
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: textController,
                maxLines: 10,
                decoration: InputDecoration(
                  hintText: '粘贴动作库的JSON数据...',
                  hintStyle: const TextStyle(color: Color(0xFF9B9A97)),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFF2E7EED)),
                  ),
                  filled: true,
                  fillColor: const Color(0xFFF7F6F3),
                  contentPadding: const EdgeInsets.all(12),
                ),
              ),

              const SizedBox(height: 24),

              // 操作按钮
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        '取消',
                        style: TextStyle(
                          color: Color(0xFF9B9A97),
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => _importLibrary(textController.text),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF2E7EED),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 0,
                      ),
                      child: const Text(
                        '导入',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 导入动作库
  void _importLibrary(String jsonData) async {
    if (jsonData.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入JSON数据'), backgroundColor: Colors.red),
      );
      return;
    }

    try {
      Navigator.of(context).pop(); // 关闭导入对话框

      final library = await _customLibraryService.importLibrary(jsonData);
      setState(() {
        // 刷新界面
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('动作库"${library.name}"导入成功'),
            backgroundColor: const Color(0xFF0F7B6C),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('导入失败: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  // 开始单词练习
  void _startWordPractice() async {
    // 筛选出所有有关联单词的动作
    final exercisesWithWords = PAOExercisesData.getAllExercises().values
        .where((ex) => ex.paoWords.isNotEmpty)
        .toList();

    if (exercisesWithWords.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('当前动作库没有可练习的单词')));
      return;
    }

    // 随机选择一个动作进行单词练习
    final randomExercise =
        exercisesWithWords[(DateTime.now().millisecondsSinceEpoch %
            exercisesWithWords.length)];
    final randomWord =
        randomExercise.paoWords[(DateTime.now().millisecondsSinceEpoch %
            randomExercise.paoWords.length)];

    // 获取单词释义
    String? wordMeaning;
    String? wordPhonetic;
    try {
      final wordMeaningService = ref.read(wordMeaningServiceProvider);
      final meaningResult = await wordMeaningService.getWordMeaning(randomWord);
      if (meaningResult != null) {
        wordMeaning = meaningResult.definition;
        wordPhonetic = meaningResult.phonetic;
      }
    } catch (e) {
      print('获取单词释义失败: $e');
    }

    if (mounted) {
      context
          .push(
            '/exercise-session',
            extra: {
              'exercises': [randomExercise],
              'paoWord': randomWord,
              'wordMeaning': wordMeaning,
              'wordPhonetic': wordPhonetic,
              'mode': ExerciseMode.pao,
              'duration': 45,
            },
          )
          .then((result) {
            if (result != null &&
                (result as Map<String, dynamic>)['completed'] == true) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('动觉练习完成！单词"$randomWord"已加强记忆'),
                    backgroundColor: const Color(0xFF2E7EED),
                  ),
                );
              }
            }
          });
    }
  }

  // 显示添加到记忆对话框
  void _showAddToMemoryDialog() {
    showDialog(
      context: context,
      builder: (context) => AddToMemoryDialog(
        onWordsAdded: (words) async {
          // 捕获当前context以避免跨async使用
          final currentContext = context;

          // 添加单词到记忆词库
          final vocabularyService = VocabularyService();
          await vocabularyService.batchAddToMemoryVocabulary(words);

          if (mounted && currentContext.mounted) {
            final messenger = ScaffoldMessenger.of(currentContext);
            messenger.showSnackBar(
              SnackBar(
                content: Text('已添加 ${words.length} 个单词到记忆词库'),
                backgroundColor: const Color(0xFF2E7EED),
              ),
            );
          }
        },
      ),
    );
  }

  // 显示自定义单词对话框
  void _showCustomWordDialog() {
    context.push('/custom-vocabulary-manager');
  }

  // 导出动作库
  void _exportLibrary() async {
    try {
      // 检查当前是否有选中的自定义动作库
      final selectedLibrary = _customLibraryService.selectedLibrary;

      if (selectedLibrary != null) {
        // 导出当前选中的自定义动作库
        await _exportSingleLibrary(selectedLibrary);
      } else {
        // 显示导出选项对话框
        _showExportOptionsDialog();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('导出失败: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  /// 导出单个动作库
  Future<void> _exportSingleLibrary(CustomActionLibrary library) async {
    try {
      // 显示进度指示器
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) =>
              const Center(child: CircularProgressIndicator()),
        );
      }

      // 导出动作库到文件
      final file = await _customLibraryService.saveLibraryToFile(library.id);

      // 关闭进度指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 显示成功消息和分享选项
      if (mounted) {
        _showExportSuccessDialog(file.path, library.name);
      }
    } catch (e) {
      // 关闭进度指示器
      if (mounted) {
        Navigator.of(context).pop();
      }
      rethrow;
    }
  }

  /// 显示导出选项对话框
  void _showExportOptionsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Text(
          '选择导出内容',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildExportOption(
              icon: Icons.library_books,
              title: '导出所有自定义动作库',
              subtitle: '导出您创建的所有动作库',
              onTap: () {
                Navigator.of(context).pop();
                _exportAllCustomLibraries();
              },
            ),
            const SizedBox(height: 12),
            _buildExportOption(
              icon: Icons.bookmark_outline,
              title: '导出记忆词库',
              subtitle: '导出您的自定义词汇',
              onTap: () {
                Navigator.of(context).pop();
                _exportMemoryVocabulary();
              },
            ),
            const SizedBox(height: 12),
            _buildExportOption(
              icon: Icons.download_outlined,
              title: '导出系统默认动作库',
              subtitle: '导出系统动作库作为模板',
              onTap: () {
                Navigator.of(context).pop();
                _exportDefaultLibrary();
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消', style: TextStyle(color: Color(0xFF787774))),
          ),
        ],
      ),
    );
  }

  /// 构建导出选项
  Widget _buildExportOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFFE3E2E0)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: const Color(0xFF2F76DA).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: const Color(0xFF2F76DA), size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      color: Color(0xFF37352F),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      color: Color(0xFF787774),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              color: Color(0xFF9B9A97),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  /// 显示导出成功对话框
  void _showExportSuccessDialog(String filePath, String libraryName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: const Color(0xFF0F7B6C).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.check_circle,
                color: Color(0xFF0F7B6C),
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                '导出成功',
                style: TextStyle(
                  color: Color(0xFF37352F),
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '动作库 "$libraryName" 已成功导出',
              style: const TextStyle(color: Color(0xFF37352F), fontSize: 14),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFFF7F6F3),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: const Color(0xFFE3E2E0)),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.folder_outlined,
                    color: Color(0xFF787774),
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      filePath,
                      style: const TextStyle(
                        color: Color(0xFF787774),
                        fontSize: 12,
                        fontFamily: 'monospace',
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('完成', style: TextStyle(color: Color(0xFF787774))),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              _shareFile(filePath);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2F76DA),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            icon: const Icon(Icons.share, size: 16),
            label: const Text('分享'),
          ),
        ],
      ),
    );
  }

  /// 导出所有自定义动作库
  Future<void> _exportAllCustomLibraries() async {
    try {
      // 显示进度指示器
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            backgroundColor: Colors.white,
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('正在导出所有自定义动作库...'),
              ],
            ),
          ),
        );
      }

      final libraries = _customLibraryService.libraries;
      if (libraries.isEmpty) {
        if (mounted) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('没有可导出的自定义动作库'),
              backgroundColor: Color(0xFF787774),
            ),
          );
        }
        return;
      }

      // 创建导出数据
      final exportData = {
        'version': '1.0',
        'exportedAt': DateTime.now().toIso8601String(),
        'appVersion': '1.0.0',
        'type': 'custom_libraries',
        'data': {
          'customLibraries': libraries.map((lib) => lib.toJson()).toList(),
        },
      };

      // 保存到文件
      final directory = await getApplicationDocumentsDirectory();
      final fileName =
          'OneDay_CustomLibraries_${DateTime.now().millisecondsSinceEpoch}.json';
      final file = File('${directory.path}/$fileName');
      await file.writeAsString(json.encode(exportData));

      // 关闭进度指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 显示成功消息
      if (mounted) {
        _showExportSuccessDialog(file.path, '所有自定义动作库');
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('导出失败: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  /// 导出记忆词库
  Future<void> _exportMemoryVocabulary() async {
    try {
      // 显示进度指示器
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            backgroundColor: Colors.white,
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('正在导出记忆词库...'),
              ],
            ),
          ),
        );
      }

      final vocabularyService = VocabularyService();
      final memoryVocab = await vocabularyService.getMemoryVocabulary();

      if (memoryVocab.isEmpty) {
        if (mounted) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('记忆词库为空，没有可导出的内容'),
              backgroundColor: Color(0xFF787774),
            ),
          );
        }
        return;
      }

      // 创建导出数据
      final exportData = {
        'version': '1.0',
        'exportedAt': DateTime.now().toIso8601String(),
        'appVersion': '1.0.0',
        'type': 'memory_vocabulary',
        'data': {
          'memoryVocabulary': memoryVocab,
          'wordCount': memoryVocab.length,
        },
      };

      // 保存到文件
      final directory = await getApplicationDocumentsDirectory();
      final fileName =
          'OneDay_MemoryVocabulary_${DateTime.now().millisecondsSinceEpoch}.json';
      final file = File('${directory.path}/$fileName');
      await file.writeAsString(json.encode(exportData));

      // 关闭进度指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 显示成功消息
      if (mounted) {
        _showExportSuccessDialog(file.path, '记忆词库 (${memoryVocab.length} 个单词)');
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('导出失败: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  /// 导出系统默认动作库
  Future<void> _exportDefaultLibrary() async {
    try {
      // 显示进度指示器
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            backgroundColor: Colors.white,
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('正在导出系统默认动作库...'),
              ],
            ),
          ),
        );
      }

      // 获取系统默认动作库数据
      final defaultExercises = PAOExercisesData.getAllExercises();

      // 转换为导出格式
      final exportActions = <String, Map<String, dynamic>>{};
      defaultExercises.forEach((letter, exercise) {
        exportActions[letter] = {
          'letter': letter,
          'nameEn': exercise.nameEn,
          'nameCn': exercise.nameCn,
          'description': exercise.description,
          'category': exercise.category,
          'scene': exercise.scenarios.join('/'),
          'keywords': exercise.paoWords,
        };
      });

      // 创建导出数据
      final exportData = {
        'version': '1.0',
        'exportedAt': DateTime.now().toIso8601String(),
        'appVersion': '1.0.0',
        'type': 'default_library_template',
        'data': {
          'library': {
            'id': 'default_template',
            'name': '系统默认动作库模板',
            'description': '基于OneDay系统默认动作库创建的模板，可用于创建自定义动作库',
            'category': '系统模板',
            'createdAt': DateTime.now().toIso8601String(),
            'lastModified': DateTime.now().toIso8601String(),
            'actions': exportActions,
          },
        },
      };

      // 保存到文件
      final directory = await getApplicationDocumentsDirectory();
      final fileName =
          'OneDay_DefaultLibraryTemplate_${DateTime.now().millisecondsSinceEpoch}.json';
      final file = File('${directory.path}/$fileName');
      await file.writeAsString(json.encode(exportData));

      // 关闭进度指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 显示成功消息
      if (mounted) {
        _showExportSuccessDialog(file.path, '系统默认动作库模板');
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('导出失败: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  /// 分享文件
  Future<void> _shareFile(String filePath) async {
    try {
      await Share.shareXFiles([XFile(filePath)]);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('分享失败: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  // 获取字母颜色
  Color _getLetterColor(String letter) {
    final colors = [
      const Color(0xFFE03E3E),
      const Color(0xFF2E7EED),
      const Color(0xFF0F7B6C),
      const Color(0xFF7C3AED),
      const Color(0xFFD9730D),
      const Color(0xFFE03E3E),
    ];
    return colors[letter.codeUnitAt(0) % colors.length];
  }

  // 获取分类颜色
  Color _getCategoryColor(String category) {
    switch (category) {
      case '健身':
        return const Color(0xFFE03E3E);
      case '篮球':
        return const Color(0xFFD9730D);
      case '足球':
        return const Color(0xFF0F7B6C);
      case '养生':
        return const Color(0xFF7C3AED);
      case '瑜伽':
        return const Color(0xFF2E7EED);
      case '拉伸':
        return const Color(0xFF2E7EED);
      case '眼保健操':
        return const Color(0xFF2E7EED);
      default:
        return const Color(0xFF9B9A97);
    }
  }
}

// PAOExercise详情对话框
class PAOExerciseDetailDialog extends ConsumerWidget {
  final PAOExercise exercise;

  const PAOExerciseDetailDialog({super.key, required this.exercise});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题栏
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                color: Color(0xFFF7F6F3),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: _getLetterColor(
                        exercise.letter,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        exercise.letter,
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: _getLetterColor(exercise.letter),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          exercise.name,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF37352F),
                          ),
                        ),
                        Text(
                          exercise.nameEn,
                          style: const TextStyle(
                            fontSize: 14,
                            color: Color(0xFF9B9A97),
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      Navigator.pop(context);
                      // 创建一个假的父widget以便调用_showEditExerciseDialog
                      showDialog(
                        context: context,
                        builder: (context) =>
                            EditExerciseDialog(exercise: exercise),
                      );
                    },
                    icon: const Icon(Icons.edit),
                    tooltip: '编辑动作',
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),

            // 内容区域
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 基本信息
                    _buildInfoRow(
                      '分类',
                      exercise.category,
                      _getCategoryColor(exercise.category),
                    ),
                    const SizedBox(height: 12),
                    _buildInfoRow(
                      '适用场景',
                      exercise.scenarios.join(' / '),
                      const Color(0xFF37352F),
                    ),
                    const SizedBox(height: 12),
                    _buildInfoRow(
                      '锻炼部位',
                      exercise.bodyPart,
                      const Color(0xFF0F7B6C),
                    ),
                    const SizedBox(height: 12),
                    _buildInfoRow(
                      '建议时长',
                      '${exercise.duration}秒',
                      const Color(0xFFD9730D),
                    ),
                    const SizedBox(height: 12),
                    _buildInfoRow(
                      '所需器材',
                      exercise.equipment,
                      const Color(0xFF7C3AED),
                    ),
                    const SizedBox(height: 16),

                    // 动作描述
                    const Text(
                      '动作要领：',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF37352F),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      exercise.description,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF37352F),
                        height: 1.5,
                      ),
                    ),

                    // PAO单词示例
                    if (exercise.paoWords.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      const Text(
                        '动觉单词示例：',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF37352F),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '以${exercise.letter}开头的单词可以用这个动作记忆：',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Color(0xFF9B9A97),
                              ),
                            ),
                            const SizedBox(height: 8),
                            Wrap(
                              spacing: 8,
                              runSpacing: 4,
                              children: exercise.paoWords
                                  .map(
                                    (word) => Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.white.withValues(
                                          alpha: 0.8,
                                        ),
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: Text(
                                        word.toUpperCase(),
                                        style: const TextStyle(
                                          fontSize: 12,
                                          fontWeight: FontWeight.w500,
                                          color: Color(0xFF2E7EED),
                                        ),
                                      ),
                                    ),
                                  )
                                  .toList(),
                            ),
                          ],
                        ),
                      ),
                    ],

                    const SizedBox(height: 20),

                    // 操作按钮
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: () {
                              Navigator.pop(context);
                              context
                                  .push(
                                    '/exercise-session',
                                    extra: {
                                      'exercises': [exercise],
                                      'mode': ExerciseMode.single,
                                      'duration': 30,
                                    },
                                  )
                                  .then((result) {
                                    if (result != null &&
                                        (result
                                                as Map<
                                                  String,
                                                  dynamic
                                                >)['completed'] ==
                                            true) {
                                      final resultMap = result;
                                      if (context.mounted) {
                                        ScaffoldMessenger.of(
                                          context,
                                        ).showSnackBar(
                                          SnackBar(
                                            content: Text(
                                              '运动完成！消耗${resultMap['calories']}卡路里，获得${resultMap['coins']}金币',
                                            ),
                                            backgroundColor: const Color(
                                              0xFF0F7B6C,
                                            ),
                                          ),
                                        );
                                      }
                                    }
                                  });
                            },
                            icon: const Icon(Icons.play_arrow),
                            label: const Text('开始运动'),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () async {
                              Navigator.pop(context);
                              if (exercise.paoWords.isNotEmpty) {
                                final paoWord = exercise.paoWords.first;

                                // 获取单词释义
                                String? wordMeaning;
                                String? wordPhonetic;
                                try {
                                  final wordMeaningService = ref.read(
                                    wordMeaningServiceProvider,
                                  );
                                  final meaningResult = await wordMeaningService
                                      .getWordMeaning(paoWord);
                                  if (meaningResult != null) {
                                    wordMeaning = meaningResult.definition;
                                    wordPhonetic = meaningResult.phonetic;
                                  }
                                } catch (e) {
                                  print('获取单词释义失败: $e');
                                }

                                if (context.mounted) {
                                  // 使用async/await处理结果，避免BuildContext跨异步间隙
                                  final result = await context.push(
                                    '/exercise-session',
                                    extra: {
                                      'exercises': [exercise],
                                      'paoWord': paoWord,
                                      'wordMeaning': wordMeaning,
                                      'wordPhonetic': wordPhonetic,
                                      'mode': ExerciseMode.pao,
                                      'duration': 45,
                                    },
                                  );

                                  if (context.mounted &&
                                      result != null &&
                                      (result
                                              as Map<
                                                String,
                                                dynamic
                                              >)['completed'] ==
                                          true) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          '动觉练习完成！单词"$paoWord"已加强记忆',
                                        ),
                                        backgroundColor: const Color(
                                          0xFF2E7EED,
                                        ),
                                      ),
                                    );
                                  }
                                }
                              } else {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(content: Text('该动作暂无关联单词')),
                                );
                              }
                            },
                            icon: const Icon(Icons.psychology),
                            label: const Text('单词练习'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF2E7EED),
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, Color color) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 70,
          child: Text(
            '$label：',
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF9B9A97),
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Color _getLetterColor(String letter) {
    final colors = [
      const Color(0xFFE03E3E),
      const Color(0xFF2E7EED),
      const Color(0xFF0F7B6C),
      const Color(0xFF7C3AED),
      const Color(0xFFD9730D),
      const Color(0xFFE03E3E),
    ];
    return colors[letter.codeUnitAt(0) % colors.length];
  }

  Color _getCategoryColor(String category) {
    switch (category) {
      case '健身':
        return const Color(0xFFE03E3E);
      case '篮球':
        return const Color(0xFFD9730D);
      case '足球':
        return const Color(0xFF0F7B6C);
      case '养生':
        return const Color(0xFF7C3AED);
      case '瑜伽':
        return const Color(0xFF2E7EED);
      case '拉伸':
        return const Color(0xFF2E7EED);
      case '眼保健操':
        return const Color(0xFF2E7EED);
      default:
        return const Color(0xFF9B9A97);
    }
  }
}

// 可编辑的PAOExercise数据类
class EditablePAOExercise {
  String letter;
  String nameEn;
  String nameCn;
  String category;
  String scene;
  String description;
  List<String> keywords;
  String bodyPart;
  int duration;
  String equipment;

  EditablePAOExercise({
    required this.letter,
    required this.nameEn,
    required this.nameCn,
    required this.category,
    required this.scene,
    required this.description,
    required this.keywords,
    required this.bodyPart,
    required this.duration,
    required this.equipment,
  });

  factory EditablePAOExercise.fromPAOExercise(PAOExercise exercise) {
    return EditablePAOExercise(
      letter: exercise.letter,
      nameEn: exercise.nameEn,
      nameCn: exercise.nameCn,
      category: exercise.category,
      scene: exercise.scene,
      description: exercise.description,
      keywords: List.from(exercise.keywords),
      bodyPart: exercise.bodyPart,
      duration: exercise.duration,
      equipment: exercise.equipment,
    );
  }

  PAOExercise toPAOExercise() {
    return PAOExercise(
      letter: letter,
      nameEn: nameEn,
      nameCn: nameCn,
      category: category,
      scene: scene,
      description: description,
      keywords: keywords,
    );
  }
}

// PAO动作编辑对话框
class EditExerciseDialog extends StatefulWidget {
  final PAOExercise exercise;

  const EditExerciseDialog({super.key, required this.exercise});

  @override
  State<EditExerciseDialog> createState() => _EditExerciseDialogState();
}

class _EditExerciseDialogState extends State<EditExerciseDialog> {
  late EditablePAOExercise _editableExercise;
  late TextEditingController _nameCnController;
  late TextEditingController _nameEnController;
  late TextEditingController _descriptionController;
  late TextEditingController _bodyPartController;
  late TextEditingController _equipmentController;
  late TextEditingController _keywordsController;

  final _formKey = GlobalKey<FormState>();

  // 可选择的类别
  final List<String> _categories = ['健身', '瑜伽', '养生', '篮球', '足球', '拉伸', '眼保健操'];

  // 可选择的场景
  final List<String> _scenes = ['简单活动', '集中训练', '简单/集中'];

  @override
  void initState() {
    super.initState();
    _editableExercise = EditablePAOExercise.fromPAOExercise(widget.exercise);

    _nameCnController = TextEditingController(text: _editableExercise.nameCn);
    _nameEnController = TextEditingController(text: _editableExercise.nameEn);
    _descriptionController = TextEditingController(
      text: _editableExercise.description,
    );
    _bodyPartController = TextEditingController(
      text: _editableExercise.bodyPart,
    );
    _equipmentController = TextEditingController(
      text: _editableExercise.equipment,
    );
    _keywordsController = TextEditingController(
      text: _editableExercise.keywords.join(', '),
    );
  }

  @override
  void dispose() {
    _nameCnController.dispose();
    _nameEnController.dispose();
    _descriptionController.dispose();
    _bodyPartController.dispose();
    _equipmentController.dispose();
    _keywordsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.95,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.9,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题栏
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                color: Color(0xFFF7F6F3),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: _getLetterColor(
                        _editableExercise.letter,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        _editableExercise.letter,
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: _getLetterColor(_editableExercise.letter),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '编辑动作',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF37352F),
                          ),
                        ),
                        Text(
                          '自定义您的动觉动作内容',
                          style: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF9B9A97),
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),

            // 编辑表单
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 中文名称
                      const Text(
                        '中文名称',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF37352F),
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _nameCnController,
                        decoration: const InputDecoration(
                          hintText: '请输入动作的中文名称',
                          filled: true,
                          fillColor: Colors.white,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.all(Radius.circular(8)),
                            borderSide: BorderSide(color: Color(0xFFE8E7E5)),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.all(Radius.circular(8)),
                            borderSide: BorderSide(color: Color(0xFFE8E7E5)),
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return '请输入中文名称';
                          }
                          return null;
                        },
                        onChanged: (value) => _editableExercise.nameCn = value,
                      ),
                      const SizedBox(height: 16),

                      // 英文名称
                      const Text(
                        '英文名称',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF37352F),
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _nameEnController,
                        decoration: const InputDecoration(
                          hintText: '请输入动作的英文名称',
                          filled: true,
                          fillColor: Colors.white,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.all(Radius.circular(8)),
                            borderSide: BorderSide(color: Color(0xFFE8E7E5)),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.all(Radius.circular(8)),
                            borderSide: BorderSide(color: Color(0xFFE8E7E5)),
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return '请输入英文名称';
                          }
                          return null;
                        },
                        onChanged: (value) => _editableExercise.nameEn = value,
                      ),
                      const SizedBox(height: 16),

                      // 分类选择
                      const Text(
                        '动作分类',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF37352F),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          border: Border.all(color: const Color(0xFFE8E7E5)),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            value: _editableExercise.category,
                            onChanged: (String? newValue) {
                              if (newValue != null) {
                                setState(() {
                                  _editableExercise.category = newValue;
                                });
                              }
                            },
                            items: _categories.map<DropdownMenuItem<String>>((
                              String value,
                            ) {
                              return DropdownMenuItem<String>(
                                value: value,
                                child: Text(value),
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // 适用场景选择
                      const Text(
                        '适用场景',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF37352F),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          border: Border.all(color: const Color(0xFFE8E7E5)),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            value: _editableExercise.scene,
                            onChanged: (String? newValue) {
                              if (newValue != null) {
                                setState(() {
                                  _editableExercise.scene = newValue;
                                });
                              }
                            },
                            items: _scenes.map<DropdownMenuItem<String>>((
                              String value,
                            ) {
                              return DropdownMenuItem<String>(
                                value: value,
                                child: Text(value),
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // 动作描述
                      const Text(
                        '动作描述',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF37352F),
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _descriptionController,
                        maxLines: 4,
                        decoration: const InputDecoration(
                          hintText: '请详细描述动作要领和执行方式',
                          filled: true,
                          fillColor: Colors.white,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.all(Radius.circular(8)),
                            borderSide: BorderSide(color: Color(0xFFE8E7E5)),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.all(Radius.circular(8)),
                            borderSide: BorderSide(color: Color(0xFFE8E7E5)),
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return '请输入动作描述';
                          }
                          return null;
                        },
                        onChanged: (value) =>
                            _editableExercise.description = value,
                      ),
                      const SizedBox(height: 16),

                      // 动觉关键词
                      const Text(
                        '动觉关键词',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF37352F),
                        ),
                      ),
                      const SizedBox(height: 4),
                      const Text(
                        '用于记忆联想的英文单词，用逗号分隔',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF9B9A97),
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _keywordsController,
                        decoration: const InputDecoration(
                          hintText: '例如：Apple, Action, Amazing',
                          filled: true,
                          fillColor: Colors.white,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.all(Radius.circular(8)),
                            borderSide: BorderSide(color: Color(0xFFE8E7E5)),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.all(Radius.circular(8)),
                            borderSide: BorderSide(color: Color(0xFFE8E7E5)),
                          ),
                        ),
                        onChanged: (value) {
                          _editableExercise.keywords = value
                              .split(',')
                              .map((e) => e.trim())
                              .where((e) => e.isNotEmpty)
                              .toList();
                        },
                      ),
                      const SizedBox(height: 16),

                      // 锻炼部位和时长
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  '锻炼部位',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Color(0xFF37352F),
                                  ),
                                ),
                                const SizedBox(height: 8),
                                TextFormField(
                                  controller: _bodyPartController,
                                  decoration: const InputDecoration(
                                    hintText: '如：全身、上肢等',
                                    filled: true,
                                    fillColor: Colors.white,
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.all(
                                        Radius.circular(8),
                                      ),
                                      borderSide: BorderSide(
                                        color: Color(0xFFE8E7E5),
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.all(
                                        Radius.circular(8),
                                      ),
                                      borderSide: BorderSide(
                                        color: Color(0xFFE8E7E5),
                                      ),
                                    ),
                                  ),
                                  onChanged: (value) =>
                                      _editableExercise.bodyPart = value,
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  '建议时长(秒)',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Color(0xFF37352F),
                                  ),
                                ),
                                const SizedBox(height: 8),
                                TextFormField(
                                  initialValue: _editableExercise.duration
                                      .toString(),
                                  keyboardType: TextInputType.number,
                                  decoration: const InputDecoration(
                                    hintText: '30',
                                    filled: true,
                                    fillColor: Colors.white,
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.all(
                                        Radius.circular(8),
                                      ),
                                      borderSide: BorderSide(
                                        color: Color(0xFFE8E7E5),
                                      ),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.all(
                                        Radius.circular(8),
                                      ),
                                      borderSide: BorderSide(
                                        color: Color(0xFFE8E7E5),
                                      ),
                                    ),
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return '请输入时长';
                                    }
                                    if (int.tryParse(value) == null) {
                                      return '请输入有效数字';
                                    }
                                    return null;
                                  },
                                  onChanged: (value) {
                                    final duration = int.tryParse(value);
                                    if (duration != null) {
                                      _editableExercise.duration = duration;
                                    }
                                  },
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),

                      // 所需器材
                      const Text(
                        '所需器材',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF37352F),
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _equipmentController,
                        decoration: const InputDecoration(
                          hintText: '如：无需器材、哑铃、瑜伽垫等',
                          filled: true,
                          fillColor: Colors.white,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.all(Radius.circular(8)),
                            borderSide: BorderSide(color: Color(0xFFE8E7E5)),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.all(Radius.circular(8)),
                            borderSide: BorderSide(color: Color(0xFFE8E7E5)),
                          ),
                        ),
                        onChanged: (value) =>
                            _editableExercise.equipment = value,
                      ),
                      const SizedBox(height: 24),

                      // 保存和取消按钮
                      Row(
                        children: [
                          Expanded(
                            child: OutlinedButton(
                              onPressed: () => Navigator.pop(context),
                              style: OutlinedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                                side: const BorderSide(
                                  color: Color(0xFFE8E7E5),
                                ),
                              ),
                              child: const Text(
                                '取消',
                                style: TextStyle(color: Color(0xFF9B9A97)),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: _saveChanges,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xFF2E7EED),
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                              ),
                              child: const Text('保存更改'),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _saveChanges() {
    if (_formKey.currentState!.validate()) {
      // 创建更新后的PAOExercise
      final updatedExercise = _editableExercise.toPAOExercise();
      Navigator.pop(context, updatedExercise);
    }
  }

  Color _getLetterColor(String letter) {
    final colors = [
      const Color(0xFFE03E3E),
      const Color(0xFF2E7EED),
      const Color(0xFF0F7B6C),
      const Color(0xFF7C3AED),
      const Color(0xFFD9730D),
      const Color(0xFFE03E3E),
    ];
    return colors[letter.codeUnitAt(0) % colors.length];
  }

  // 已删除未使用的 getWordMeaningData 方法
}

/// 动作库侧边栏组件 - 参考CategorySidebar的抽屉式设计
class ActionLibrarySidebar extends StatefulWidget {
  final bool isVisible;
  final VoidCallback onClose;
  final Function(String) onCategorySelected;
  final String selectedCategory;
  final TextEditingController searchController;
  final VoidCallback onSearchChanged;
  final CustomExerciseCategoryManager customCategoryManager;
  final ActionLibraryCategoryManager treeCategoryManager;
  final CustomActionLibraryService customLibraryService;
  final VoidCallback onCategoriesChanged;

  const ActionLibrarySidebar({
    super.key,
    required this.isVisible,
    required this.onClose,
    required this.onCategorySelected,
    required this.selectedCategory,
    required this.searchController,
    required this.onSearchChanged,
    required this.customCategoryManager,
    required this.treeCategoryManager,
    required this.customLibraryService,
    required this.onCategoriesChanged,
  });

  @override
  State<ActionLibrarySidebar> createState() => _ActionLibrarySidebarState();
}

class _ActionLibrarySidebarState extends State<ActionLibrarySidebar> {
  bool _isSearchExpanded = false;
  final FocusNode _searchFocusNode = FocusNode();

  @override
  void dispose() {
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _toggleSearch() {
    setState(() {
      _isSearchExpanded = !_isSearchExpanded;
      if (_isSearchExpanded) {
        _searchFocusNode.requestFocus();
      } else {
        _searchFocusNode.unfocus();
        widget.searchController.clear();
        widget.onSearchChanged();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    const double sidebarWidth = 280;

    return Stack(
      children: [
        // 半透明遮罩
        if (widget.isVisible)
          GestureDetector(
            onTap: widget.onClose,
            child: Container(
              color: Colors.black.withValues(alpha: 0.3),
              width: double.infinity,
              height: double.infinity,
            ),
          ),

        // 侧边栏
        AnimatedPositioned(
          duration: const Duration(milliseconds: 250),
          curve: Curves.easeInOut,
          left: widget.isVisible ? 0 : -sidebarWidth,
          top: 0,
          bottom: 0,
          width: sidebarWidth,
          child: Material(
            elevation: 8,
            child: Container(
              color: Colors.white,
              child: SafeArea(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 顶部操作栏
                    _buildTopBar(),
                    // 分类列表
                    Expanded(
                      child: ListView(
                        padding: EdgeInsets.zero,
                        children: _buildCategoryList(),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建顶部操作栏 - 优化空间和字体
  Widget _buildTopBar() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 14,
        vertical: 10,
      ), // 减少内边距
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Color(0xFFE3E2E0), width: 0.8), // 减少边框宽度
        ),
      ),
      child: Column(
        children: [
          // 标题栏和操作按钮
          Row(
            children: [
              const Text(
                '动作库',
                style: TextStyle(
                  fontSize: 17, // 从18减少到17
                  fontWeight: FontWeight.w700, // 从w600增加到w700，保持视觉重量
                  color: Color(0xFF37352F),
                  letterSpacing: -0.3, // 添加字母间距优化
                ),
              ),
              const Spacer(),
              // 添加自定义动作按钮
              IconButton(
                icon: const Icon(
                  Icons.add_circle_outline,
                  color: Color(0xFF37352F),
                  size: 18,
                ),
                onPressed: _showAddCustomActionDialog,
                tooltip: '添加自定义动作',
                padding: const EdgeInsets.all(3),
                constraints: const BoxConstraints(minWidth: 30, minHeight: 30),
              ),
              const SizedBox(width: 2),
              // 搜索按钮 - 优化尺寸
              IconButton(
                icon: Icon(
                  _isSearchExpanded ? Icons.search_off : Icons.search,
                  color: const Color(0xFF37352F),
                  size: 18, // 从20减少到18
                ),
                onPressed: _toggleSearch,
                tooltip: _isSearchExpanded ? '关闭搜索' : '搜索动作',
                padding: const EdgeInsets.all(3), // 从4减少到3
                constraints: const BoxConstraints(
                  minWidth: 30,
                  minHeight: 30,
                ), // 从32减少到30
              ),
            ],
          ),
          // 搜索框（展开时显示）- 优化尺寸和字体
          if (_isSearchExpanded) ...[
            const SizedBox(height: 6), // 从8减少到6
            TextField(
              controller: widget.searchController,
              focusNode: _searchFocusNode,
              onChanged: (_) => widget.onSearchChanged(),
              style: const TextStyle(
                fontSize: 14, // 添加输入文字字体大小
                color: Color(0xFF37352F),
              ),
              decoration: InputDecoration(
                hintText: '搜索动作或单词...',
                hintStyle: const TextStyle(
                  color: Color(0xFF9B9A97),
                  fontSize: 14, // 明确设置提示文字大小
                ),
                prefixIcon: const Icon(
                  Icons.search,
                  color: Color(0xFF9B9A97),
                  size: 16,
                ), // 从18减少到16
                suffixIcon: widget.searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(
                          Icons.clear,
                          color: Color(0xFF9B9A97),
                          size: 16,
                        ), // 从18减少到16
                        onPressed: () {
                          widget.searchController.clear();
                          widget.onSearchChanged();
                        },
                        padding: const EdgeInsets.all(2), // 添加padding控制
                      )
                    : null,
                filled: true,
                fillColor: const Color(0xFFF7F6F3),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(6), // 从8减少到6
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 10,
                  vertical: 6,
                ), // 从12,8减少到10,6
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建分类列表
  List<Widget> _buildCategoryList() {
    final widgets = <Widget>[];

    // 添加"全部"分类
    widgets.add(_buildCategoryItem('全部', '✨', false, 0));

    // 添加自定义分类（使用层级结构）
    final hierarchicalCustomCategories = widget.customCategoryManager
        .getHierarchicalCategories();
    for (final customCategory in hierarchicalCustomCategories) {
      final isSelected = widget.selectedCategory == customCategory.name;
      widgets.add(_buildCustomCategoryItem(customCategory, isSelected));
    }

    // 添加树形分类
    for (final category in widget.treeCategoryManager.categories) {
      widgets.addAll(_buildTreeNode(category));
    }

    return widgets;
  }

  /// 构建树形节点
  List<Widget> _buildTreeNode(ActionLibraryCategoryNode node) {
    final widgets = <Widget>[];

    // 添加当前节点
    widgets.add(_buildTreeCategoryItem(node));

    // 如果展开，添加子节点和对应的自定义动作库
    if (node.isExpanded) {
      // 添加子分类节点
      for (final child in node.children) {
        widgets.addAll(_buildTreeNode(child));
      }

      // 添加属于当前分类的自定义动作库
      final librariesForCategory = _getLibrariesForCategory(node.title);
      for (final library in librariesForCategory) {
        widgets.add(_buildCustomLibraryItem(library, node.level + 1));
      }
    }

    return widgets;
  }

  /// 获取属于指定分类的自定义动作库
  List<CustomActionLibrary> _getLibrariesForCategory(String categoryName) {
    return widget.customLibraryService.libraries
        .where((library) => library.category == categoryName)
        .toList();
  }

  /// 构建自定义动作库项
  Widget _buildCustomLibraryItem(CustomActionLibrary library, int level) {
    return Material(
      color: Colors.transparent,
      child: Container(
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(color: Color(0xFFE3E2E0), width: 0.4),
          ),
        ),
        child: Row(
          children: [
            // 缩进空间
            SizedBox(width: 14.0 + (level * 20.0) + 32),

            // 可点击的动作库名称区域
            Expanded(
              child: InkWell(
                onTap: () => _openCustomLibrary(library),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    vertical: 10,
                    horizontal: 8,
                  ),
                  child: Row(
                    children: [
                      // 动作库图标
                      const Icon(
                        Icons.library_books,
                        size: 16,
                        color: Color(0xFF9B9A97),
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              library.name,
                              style: const TextStyle(
                                fontSize: 14,
                                color: Color(0xFF37352F),
                                fontWeight: FontWeight.w500,
                                letterSpacing: -0.1,
                              ),
                            ),
                            if (library.description.isNotEmpty)
                              Text(
                                library.description,
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Color(0xFF9B9A97),
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                          ],
                        ),
                      ),
                      // 动作数量标识
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFF2F76DA).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Text(
                          '${library.actions.length}',
                          style: const TextStyle(
                            fontSize: 10,
                            color: Color(0xFF2F76DA),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // 更多操作按钮
            PopupMenuButton<String>(
              onSelected: (action) =>
                  _handleCustomLibraryAction(action, library),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(
                        Icons.edit_outlined,
                        size: 16,
                        color: Color(0xFF2F76DA),
                      ),
                      SizedBox(width: 8),
                      Text('编辑动作库'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'change_category',
                  child: Row(
                    children: [
                      Icon(
                        Icons.folder_outlined,
                        size: 16,
                        color: Color(0xFF2F76DA),
                      ),
                      SizedBox(width: 8),
                      Text('更改分类'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'share',
                  child: Row(
                    children: [
                      Icon(
                        Icons.share_outlined,
                        size: 16,
                        color: Color(0xFF2F76DA),
                      ),
                      SizedBox(width: 8),
                      Text('分享动作库'),
                    ],
                  ),
                ),
                const PopupMenuDivider(),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete_outline, size: 16, color: Colors.red),
                      SizedBox(width: 8),
                      Text('删除动作库', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
              icon: const Icon(
                Icons.more_horiz,
                color: Color(0xFF9B9A97),
                size: 20,
              ),
              tooltip: '更多操作',
              padding: EdgeInsets.zero,
              splashRadius: 20,
            ),
          ],
        ),
      ),
    );
  }

  /// 打开自定义动作库
  void _openCustomLibrary(CustomActionLibrary library) {
    // 导航到自定义动作库编辑页面
    context
        .push(
          '/custom-library-editor',
          extra: {
            'library': library,
            'customLibraryService': widget.customLibraryService,
          },
        )
        .then((result) {
          // 从编辑页面返回后刷新
          widget.onCategoriesChanged();

          // 如果有保存更改，自动选择该动作库分类并关闭侧边栏
          if (result == true) {
            // 通过回调函数选择分类，这会自动关闭侧边栏并刷新数据
            widget.onCategorySelected(library.name);

            print('🎯 自动选择自定义动作库分类: ${library.name}');
          }
        });
  }

  /// 处理自定义分类操作
  void _handleCustomCategoryAction(
    String action,
    CustomExerciseCategory category,
  ) {
    switch (action) {
      case 'edit':
        _editCustomCategory(category);
        break;
      case 'add_child':
        _addChildCustomCategory(category);
        break;
      case 'share':
        _shareCustomCategory(category);
        break;
      case 'delete':
        _deleteCustomCategory(category);
        break;
    }
  }

  /// 编辑自定义分类
  void _editCustomCategory(CustomExerciseCategory category) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _EditCustomCategoryDialog(
        category: category,
        customCategoryManager: widget.customCategoryManager,
        onCategoryUpdated: (updatedCategory) {
          // 通知父组件刷新
          widget.onCategoriesChanged();

          // 触发重建以显示更新后的分类
          setState(() {});
        },
      ),
    );
  }

  /// 添加子分类
  void _addChildCustomCategory(CustomExerciseCategory parentCategory) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _AddCustomCategoryDialog(
        customCategoryManager: widget.customCategoryManager,
        parentCustomCategory: parentCategory,
        onCategoryAdded: (category) {
          // 通知父组件刷新
          widget.onCategoriesChanged();

          // 触发重建以显示新的子分类
          setState(() {});
        },
      ),
    );
  }

  /// 分享自定义分类
  void _shareCustomCategory(CustomExerciseCategory category) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Text('共享到社区'),
        content: Text('确定要将分类"${category.name}"共享到社区吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => _performShareCustomCategory(category),
            child: const Text('共享'),
          ),
        ],
      ),
    );
  }

  /// 执行自定义分类共享
  void _performShareCustomCategory(CustomExerciseCategory category) async {
    // 捕获当前context以避免跨async使用
    final currentContext = context;

    try {
      // 关闭确认对话框
      Navigator.of(currentContext).pop();

      // 显示加载提示
      if (mounted) {
        ScaffoldMessenger.of(currentContext).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                SizedBox(width: 12),
                Text('正在共享到社区...'),
              ],
            ),
            backgroundColor: Color(0xFF2E7EED),
            duration: Duration(seconds: 2),
          ),
        );
      }

      // 实际的共享逻辑
      await _uploadCategoryToCommunity(category);

      // 显示成功提示
      if (mounted && currentContext.mounted) {
        ScaffoldMessenger.of(currentContext).showSnackBar(
          SnackBar(
            content: Text('分类"${category.name}"已成功共享到社区'),
            backgroundColor: const Color(0xFF0F7B6C),
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // 显示错误提示
      if (mounted && currentContext.mounted) {
        ScaffoldMessenger.of(currentContext).showSnackBar(
          SnackBar(
            content: Text('共享失败：${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  /// 上传分类到社区
  Future<void> _uploadCategoryToCommunity(
    CustomExerciseCategory category,
  ) async {
    try {
      // 获取社区存储服务实例
      final storageService = CommunityStorageService.instance;

      // 获取下一个帖子ID
      final postId = await storageService.getNextPostId();

      // 创建当前用户信息（实际应用中应该从用户状态获取）
      final currentUser = UserInfo(
        id: 1,
        username: '动作库创作者',
        avatar: '',
        isVerified: false,
      );

      // 构建分类信息内容
      final categoryInfo = StringBuffer();
      categoryInfo.writeln('🏃‍♂️ 动作库分类分享');
      categoryInfo.writeln('');
      categoryInfo.writeln('📂 分类名称：${category.name}');
      categoryInfo.writeln(
        '📝 分类描述：${category.description?.isNotEmpty == true ? category.description : '暂无描述'}',
      );
      categoryInfo.writeln('🎯 动作数量：${category.exercises.length} 个');
      categoryInfo.writeln('');

      if (category.exercises.isNotEmpty) {
        categoryInfo.writeln('💪 包含动作：');
        int count = 0;
        for (final exercise in category.exercises.values) {
          if (count >= 5) {
            categoryInfo.writeln('... 还有 ${category.exercises.length - 5} 个动作');
            break;
          }
          categoryInfo.writeln(
            '• ${exercise.letter} - ${exercise.name} (${exercise.nameEn})',
          );
          count++;
        }
        categoryInfo.writeln('');
      }

      categoryInfo.writeln('🔗 来自OneDay动作库管理系统');
      categoryInfo.writeln('');
      categoryInfo.writeln('#动作库分类 #动觉记忆 #健身运动 #学习方法');

      // 创建社区帖子
      final newPost = CommunityPost(
        id: postId,
        author: currentUser,
        content: categoryInfo.toString(),
        type: PostType.experience, // 分类分享属于经验分享类型
        tags: ['动作库分类', '动觉记忆', '健身运动', '学习方法'],
        images: [], // 暂时不包含图片
        likeCount: 0,
        commentCount: 0,
        shareCount: 0,
        createdAt: DateTime.now(),
        isLiked: false,
      );

      // 保存到社区存储
      final success = await storageService.addPost(newPost);
      if (!success) {
        throw Exception('保存到社区失败');
      }

      print('✅ 分类"${category.name}"已成功共享到社区，帖子ID: $postId');
    } catch (e) {
      print('❌ 上传分类到社区失败: $e');
      rethrow;
    }
  }

  /// 删除自定义分类
  void _deleteCustomCategory(CustomExerciseCategory category) {
    // 检查是否有子分类
    final hasChildren = category.hasChildren;
    final childrenCount = category.childrenIds.length;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Text('确认删除'),
        content: Text(
          '确定要删除分类"${category.name}"吗？${hasChildren ? '\n注意：这将同时删除所有 $childrenCount 个子分类。' : ''}\n此操作不可撤销。',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              // 捕获当前context以避免跨async使用
              final currentContext = context;
              Navigator.of(currentContext).pop();

              try {
                // 递归删除所有子分类
                await _deleteCustomCategoryRecursive(category);

                // 从父分类中移除引用（如果有父分类）
                if (category.parentId != null) {
                  final parent = widget.customCategoryManager.findCategoryById(
                    category.parentId!,
                  );
                  if (parent != null) {
                    final updatedParent = parent.removeChild(category.id);
                    await widget.customCategoryManager.updateCategory(
                      updatedParent,
                    );
                  }
                }

                // 删除当前分类
                await widget.customCategoryManager.deleteCategory(category.id);

                // 通知父组件刷新
                widget.onCategoriesChanged();
                setState(() {});
              } catch (e) {
                // 删除失败
                if (mounted && currentContext.mounted) {
                  ScaffoldMessenger.of(currentContext).showSnackBar(
                    SnackBar(
                      content: Text('删除分类"${category.name}"失败：${e.toString()}'),
                      backgroundColor: Colors.red,
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  /// 处理自定义动作库操作
  void _handleCustomLibraryAction(String action, CustomActionLibrary library) {
    switch (action) {
      case 'edit':
        _openCustomLibrary(library);
        break;
      case 'change_category':
        _changeLibraryCategory(library);
        break;
      case 'share':
        _shareCustomLibrary(library);
        break;
      case 'delete':
        _deleteCustomLibrary(library);
        break;
    }
  }

  /// 更改动作库分类
  Future<void> _changeLibraryCategory(CustomActionLibrary library) async {
    // 调试信息
    print('🔧 开始更改动作库分类');
    print('📚 动作库: ${library.name}');
    print('📂 当前分类: ${library.category}');
    print('📋 可用分类: ${widget.treeCategoryManager.getAllCategoryNames()}');

    final String? newCategory = await showDialog<String>(
      context: context,
      builder: (context) => _CategorySelectionDialog(
        categoryManager: widget.treeCategoryManager,
        currentCategory: library.category,
        libraryTitle: library.name,
      ),
    );

    if (newCategory != null && newCategory != library.category) {
      try {
        final updatedLibrary = library.copyWith(category: newCategory);
        await widget.customLibraryService.updateLibrary(updatedLibrary);

        // 通知父组件刷新
        widget.onCategoriesChanged();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('动作库"${library.name}"已移动到"$newCategory"分类'),
              backgroundColor: const Color(0xFF0F7B6C),
              behavior: SnackBarBehavior.floating,
              duration: const Duration(seconds: 1),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('更改分类失败: $e'),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
              duration: const Duration(seconds: 1),
            ),
          );
        }
      }
    }
  }

  /// 分享自定义动作库
  void _shareCustomLibrary(CustomActionLibrary library) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('分享动作库'),
        content: Text('确定要分享动作库"${library.name}"吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('动作库"${library.name}"已分享'),
                  backgroundColor: const Color(0xFF0F7B6C),
                ),
              );
            },
            child: const Text('分享'),
          ),
        ],
      ),
    );
  }

  /// 删除自定义动作库
  void _deleteCustomLibrary(CustomActionLibrary library) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除动作库"${library.name}"吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              // 保存context引用
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              Navigator.of(context).pop();
              await widget.customLibraryService.deleteLibrary(library.id);
              widget.onCategoriesChanged();
              if (mounted) {
                scaffoldMessenger.showSnackBar(
                  SnackBar(
                    content: Text('动作库"${library.name}"已删除'),
                    backgroundColor: const Color(0xFF0F7B6C),
                  ),
                );
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  /// 构建简单分类项（用于"全部"）
  Widget _buildCategoryItem(
    String name,
    String icon,
    bool isSelected,
    int level,
  ) {
    return Material(
      color: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFF2F76DA).withValues(alpha: 0.08)
              : null,
          border: const Border(
            bottom: BorderSide(color: Color(0xFFE3E2E0), width: 0.4),
          ),
        ),
        child: InkWell(
          onTap: () => widget.onCategorySelected(name),
          child: Container(
            padding: EdgeInsets.only(
              left: 14.0 + (level * 20.0),
              right: 14,
              top: 10,
              bottom: 10,
            ),
            child: Row(
              children: [
                Text(icon, style: const TextStyle(fontSize: 18)),
                const SizedBox(width: 10),
                Expanded(
                  child: Text(
                    name,
                    style: TextStyle(
                      fontSize: 16,
                      color: isSelected
                          ? const Color(0xFF2F76DA)
                          : const Color(0xFF37352F),
                      fontWeight: isSelected
                          ? FontWeight.w700
                          : FontWeight.w500,
                      letterSpacing: -0.1,
                    ),
                  ),
                ),
                if (isSelected)
                  const Icon(Icons.check, color: Color(0xFF2F76DA), size: 18),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建自定义分类项
  Widget _buildCustomCategoryItem(
    CustomExerciseCategory category,
    bool isSelected,
  ) {
    final hasChildren = category.hasChildren;

    return Material(
      color: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFF2F76DA).withValues(alpha: 0.08)
              : null,
          border: const Border(
            bottom: BorderSide(color: Color(0xFFE3E2E0), width: 0.4),
          ),
        ),
        child: Row(
          children: [
            // 展开/折叠按钮（与系统分类保持一致）
            if (hasChildren)
              IconButton(
                icon: Icon(
                  category.isExpanded ? Icons.expand_more : Icons.chevron_right,
                  size: 20,
                  color: const Color(0xFF9B9A97),
                ),
                onPressed: () async {
                  await widget.customCategoryManager.toggleCategoryExpanded(
                    category.id,
                  );
                  setState(() {
                    // 触发重建以显示/隐藏子分类
                  });
                },
                padding: EdgeInsets.only(left: 14.0 + (category.level * 20.0)),
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
              )
            else
              SizedBox(width: 14.0 + (category.level * 20.0) + 32),

            // 可点击的分类名称区域
            Expanded(
              child: InkWell(
                onTap: () => widget.onCategorySelected(category.name),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    vertical: 10,
                    horizontal: 8,
                  ),
                  child: Row(
                    children: [
                      // 分类图标
                      Text(category.icon, style: const TextStyle(fontSize: 18)),
                      const SizedBox(width: 10),
                      Expanded(
                        child: Text(
                          category.name,
                          style: TextStyle(
                            fontSize: 16,
                            color: isSelected
                                ? const Color(0xFF2F76DA)
                                : const Color(0xFF37352F),
                            fontWeight: isSelected
                                ? FontWeight.w700
                                : FontWeight.w500,
                            letterSpacing: -0.1,
                          ),
                        ),
                      ),
                      if (isSelected)
                        const Icon(
                          Icons.check,
                          color: Color(0xFF2F76DA),
                          size: 18,
                        ),
                    ],
                  ),
                ),
              ),
            ),

            // 更多操作按钮（与系统分类保持一致）
            PopupMenuButton<String>(
              onSelected: (action) =>
                  _handleCustomCategoryAction(action, category),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(
                        Icons.edit_outlined,
                        size: 16,
                        color: Color(0xFF2F76DA),
                      ),
                      SizedBox(width: 8),
                      Text('编辑分类'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'add_child',
                  child: Row(
                    children: [
                      Icon(
                        Icons.create_new_folder_outlined,
                        size: 16,
                        color: Color(0xFF2F76DA),
                      ),
                      SizedBox(width: 8),
                      Text('添加子分类'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'share',
                  child: Row(
                    children: [
                      Icon(
                        Icons.share_outlined,
                        size: 16,
                        color: Color(0xFF2F76DA),
                      ),
                      SizedBox(width: 8),
                      Text('共享到社区'),
                    ],
                  ),
                ),
                const PopupMenuDivider(),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete_outline, size: 16, color: Colors.red),
                      SizedBox(width: 8),
                      Text('删除分类', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
              icon: const Icon(
                Icons.more_horiz,
                color: Color(0xFF9B9A97),
                size: 20,
              ),
              tooltip: '更多操作',
              padding: EdgeInsets.zero,
              splashRadius: 20,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建树形分类项
  Widget _buildTreeCategoryItem(ActionLibraryCategoryNode node) {
    final isSelected = widget.selectedCategory == node.title;
    final hasChildren = node.children.isNotEmpty;

    return Material(
      color: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFF2F76DA).withValues(alpha: 0.08)
              : null,
          border: const Border(
            bottom: BorderSide(color: Color(0xFFE3E2E0), width: 0.4),
          ),
        ),
        child: Row(
          children: [
            // 展开/折叠按钮
            if (hasChildren)
              IconButton(
                icon: Icon(
                  node.isExpanded ? Icons.expand_more : Icons.chevron_right,
                  size: 20,
                  color: const Color(0xFF9B9A97),
                ),
                onPressed: () {
                  setState(() {
                    node.isExpanded = !node.isExpanded;
                  });
                },
                padding: EdgeInsets.only(left: 14.0 + (node.level * 20.0)),
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
              )
            else
              SizedBox(width: 14.0 + (node.level * 20.0) + 32),

            // 可点击的分类名称区域
            Expanded(
              child: InkWell(
                onTap: () => widget.onCategorySelected(node.title),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    vertical: 10,
                    horizontal: 8,
                  ),
                  child: Row(
                    children: [
                      // 分类图标
                      Text(
                        _getCategoryIcon(node),
                        style: const TextStyle(fontSize: 18),
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: Text(
                          node.title,
                          style: TextStyle(
                            fontSize: 16,
                            color: isSelected
                                ? const Color(0xFF2F76DA)
                                : const Color(0xFF37352F),
                            fontWeight: isSelected
                                ? FontWeight.w700
                                : FontWeight.w500,
                            letterSpacing: -0.1,
                          ),
                        ),
                      ),
                      if (isSelected)
                        const Icon(
                          Icons.check,
                          color: Color(0xFF2F76DA),
                          size: 18,
                        ),
                    ],
                  ),
                ),
              ),
            ),

            // 更多操作按钮
            PopupMenuButton<String>(
              onSelected: (action) => _handleTreeCategoryAction(action, node),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(
                        Icons.edit_outlined,
                        size: 16,
                        color: Color(0xFF2F76DA),
                      ),
                      SizedBox(width: 8),
                      Text('编辑分类'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'add_child',
                  child: Row(
                    children: [
                      Icon(
                        Icons.create_new_folder_outlined,
                        size: 16,
                        color: Color(0xFF2F76DA),
                      ),
                      SizedBox(width: 8),
                      Text('添加子分类'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'share',
                  child: Row(
                    children: [
                      Icon(
                        Icons.share_outlined,
                        size: 16,
                        color: Color(0xFF2F76DA),
                      ),
                      SizedBox(width: 8),
                      Text('共享到社区'),
                    ],
                  ),
                ),
                const PopupMenuDivider(),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete_outline, size: 16, color: Colors.red),
                      SizedBox(width: 8),
                      Text('删除分类', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
              icon: const Icon(
                Icons.more_horiz,
                color: Color(0xFF9B9A97),
                size: 20,
              ),
              tooltip: '更多操作',
              padding: EdgeInsets.zero,
              splashRadius: 20,
            ),
          ],
        ),
      ),
    );
  }

  /// 递归删除自定义分类及其所有子分类
  Future<void> _deleteCustomCategoryRecursive(
    CustomExerciseCategory category,
  ) async {
    // 先删除所有子分类
    final children = widget.customCategoryManager.getChildCategories(
      category.id,
    );
    for (final child in children) {
      await _deleteCustomCategoryRecursive(child);
    }

    // 删除当前分类
    await widget.customCategoryManager.deleteCategory(category.id);
  }

  /// 获取分类图标
  String _getCategoryIcon(ActionLibraryCategoryNode node) {
    // 优先使用存储的图标
    if (node.icon != null && node.icon!.isNotEmpty) {
      return node.icon!;
    }

    // 如果没有存储图标，则使用默认映射
    switch (node.title) {
      case '健身':
        return '💪';
      case '运动':
        return '🏃‍♂️';
      case '养生':
        return '☯️';
      case '日常':
        return '🏠';
      case '力量训练':
        return '🏋️‍♂️';
      case '有氧运动':
        return '🏃‍♀️';
      case '柔韧性':
        return '🤸‍♀️';
      case '球类运动':
        return '🏐';
      case '篮球':
        return '🏀';
      case '足球':
        return '⚽️';
      case '水上运动':
        return '🏊‍♂️';
      case '户外运动':
        return '🚴‍♂️';
      case '瑜伽':
        return '🧘‍♀️';
      case '冥想':
        return '🧘‍♂️';
      case '传统养生':
        return '🍃';
      case '护眼':
        return '👁️';
      case '拉伸':
        return '💼';
      case '呼吸练习':
        return '💨';
      default:
        return '📁';
    }
  }

  // 已删除未使用的 _showHelpDialog 方法

  /// 显示添加自定义动作分类对话框
  void _showAddCustomActionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _AddCustomCategoryDialog(
        customCategoryManager: widget.customCategoryManager,
        onCategoryAdded: (category) {
          // 通知父组件刷新
          widget.onCategoriesChanged();
        },
      ),
    );
  }

  /// 处理树形分类操作
  void _handleTreeCategoryAction(
    String action,
    ActionLibraryCategoryNode node,
  ) {
    switch (action) {
      case 'edit':
        _editTreeCategory(node);
        break;
      case 'add_child':
        _addChildCategory(node);
        break;
      case 'share':
        _shareTreeCategory(node);
        break;
      case 'delete':
        _deleteTreeCategory(node);
        break;
    }
  }

  /// 编辑树形分类
  void _editTreeCategory(ActionLibraryCategoryNode node) {
    final controller = TextEditingController(text: node.title);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('编辑分类'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: '分类名称',
            border: OutlineInputBorder(),
          ),
          onSubmitted: (value) {
            if (value.trim().isNotEmpty) {
              setState(() {
                node.title = value.trim();
              });
              widget.onCategoriesChanged();
              Navigator.of(context).pop();
            }
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              final newTitle = controller.text.trim();
              if (newTitle.isNotEmpty) {
                setState(() {
                  node.title = newTitle;
                });
                widget.onCategoriesChanged();
              }
              Navigator.of(context).pop();
            },
            child: const Text('保存'),
          ),
        ],
      ),
    );
  }

  /// 添加子分类
  void _addChildCategory(ActionLibraryCategoryNode parent) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => _AddCustomCategoryDialog(
        treeCategoryManager: widget.treeCategoryManager,
        parentTreeCategory: parent,
        onCategoriesChanged: () {
          // 通知父组件刷新
          widget.onCategoriesChanged();

          // 触发重建以显示新的子分类
          setState(() {});
        },
      ),
    );
  }

  /// 共享树形分类
  void _shareTreeCategory(ActionLibraryCategoryNode node) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Text('共享到社区'),
        content: Text('确定要将分类"${node.title}"共享到社区吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => _performShareTreeCategory(node),
            child: const Text('共享'),
          ),
        ],
      ),
    );
  }

  /// 执行树形分类共享
  void _performShareTreeCategory(ActionLibraryCategoryNode node) async {
    // 捕获当前context以避免跨async使用
    final currentContext = context;

    try {
      // 关闭确认对话框
      Navigator.of(currentContext).pop();

      // 显示加载提示
      if (mounted) {
        ScaffoldMessenger.of(currentContext).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                SizedBox(width: 12),
                Text('正在共享到社区...'),
              ],
            ),
            backgroundColor: Color(0xFF2E7EED),
            duration: Duration(seconds: 2),
          ),
        );
      }

      // 实际的共享逻辑
      await _uploadSystemCategoryToCommunity(node);

      // 显示成功提示
      if (mounted && currentContext.mounted) {
        ScaffoldMessenger.of(currentContext).showSnackBar(
          SnackBar(
            content: Text('分类"${node.title}"已成功共享到社区'),
            backgroundColor: const Color(0xFF0F7B6C),
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // 显示错误提示
      if (mounted && currentContext.mounted) {
        ScaffoldMessenger.of(currentContext).showSnackBar(
          SnackBar(
            content: Text('共享失败：${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  /// 上传系统分类到社区
  Future<void> _uploadSystemCategoryToCommunity(
    ActionLibraryCategoryNode node,
  ) async {
    try {
      // 获取社区存储服务实例
      final storageService = CommunityStorageService.instance;

      // 获取下一个帖子ID
      final postId = await storageService.getNextPostId();

      // 创建当前用户信息（实际应用中应该从用户状态获取）
      final currentUser = UserInfo(
        id: 1,
        username: '动作库创作者',
        avatar: '',
        isVerified: false,
      );

      // 获取系统分类的动作数据
      final categoryExercises = _getSystemCategoryExercises(node.title);

      // 构建分类信息内容
      final categoryInfo = StringBuffer();
      categoryInfo.writeln('🏃‍♂️ 系统动作库分类分享');
      categoryInfo.writeln('');
      categoryInfo.writeln('📂 分类名称：${node.title}');
      categoryInfo.writeln('📝 分类类型：系统内置分类');
      categoryInfo.writeln('🎯 动作数量：${categoryExercises.length} 个');
      categoryInfo.writeln('');

      if (categoryExercises.isNotEmpty) {
        categoryInfo.writeln('💪 包含动作：');
        int count = 0;
        for (final exercise in categoryExercises.values) {
          if (count >= 5) {
            categoryInfo.writeln('... 还有 ${categoryExercises.length - 5} 个动作');
            break;
          }
          categoryInfo.writeln(
            '• ${exercise.letter} - ${exercise.name} (${exercise.nameEn})',
          );
          count++;
        }
        categoryInfo.writeln('');
      }

      categoryInfo.writeln('🔗 来自OneDay系统动作库');
      categoryInfo.writeln('');
      categoryInfo.writeln('#系统动作库 #${node.title} #动觉记忆 #学习方法');

      // 创建社区帖子
      final newPost = CommunityPost(
        id: postId,
        author: currentUser,
        content: categoryInfo.toString(),
        type: PostType.experience, // 分类分享属于经验分享类型
        tags: ['系统动作库', node.title, '动觉记忆', '学习方法'],
        images: [], // 暂时不包含图片
        likeCount: 0,
        commentCount: 0,
        shareCount: 0,
        createdAt: DateTime.now(),
        isLiked: false,
      );

      // 保存到社区存储
      final success = await storageService.addPost(newPost);
      if (!success) {
        throw Exception('保存到社区失败');
      }

      print('✅ 系统分类"${node.title}"已成功共享到社区，帖子ID: $postId');
    } catch (e) {
      print('❌ 上传系统分类到社区失败: $e');
      rethrow;
    }
  }

  /// 获取系统分类的动作数据
  Map<String, PAOExercise> _getSystemCategoryExercises(String categoryName) {
    // 从PAOExercisesData获取指定分类的动作
    final exercises = PAOExercisesData.getExercisesByCategory(categoryName);
    return exercises ?? {};
  }

  /// 删除树形分类
  void _deleteTreeCategory(ActionLibraryCategoryNode node) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text(
          '确定要删除分类"${node.title}"吗？${node.children.isNotEmpty ? '\n注意：这将同时删除所有子分类。' : ''}',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                widget.treeCategoryManager.deleteNode(node.id);
              });
              widget.onCategoriesChanged();
              Navigator.of(context).pop();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  // 已删除未使用的 _handleCategoryAction 方法
}

/// 编辑自定义分类对话框
class _EditCustomCategoryDialog extends StatefulWidget {
  final CustomExerciseCategory category;
  final CustomExerciseCategoryManager customCategoryManager;
  final Function(CustomExerciseCategory) onCategoryUpdated;

  const _EditCustomCategoryDialog({
    required this.category,
    required this.customCategoryManager,
    required this.onCategoryUpdated,
  });

  @override
  State<_EditCustomCategoryDialog> createState() =>
      _EditCustomCategoryDialogState();
}

class _EditCustomCategoryDialogState extends State<_EditCustomCategoryDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  String _selectedIcon = '🏃‍♂️'; // 默认图标
  bool _isLoading = false;
  String? _errorMessage;

  // 可选择的图标列表（与创建对话框保持一致）
  final List<String> _availableIcons = [
    '🏃‍♂️',
    '🤸‍♂️',
    '🏋️‍♂️',
    '🚴‍♂️',
    '🏊‍♂️',
    '🧘‍♂️',
    '🤾‍♂️',
    '🏌️‍♂️',
    '🏃‍♀️',
    '🤸‍♀️',
    '🏋️‍♀️',
    '🚴‍♀️',
    '🏊‍♀️',
    '🧘‍♀️',
    '🤾‍♀️',
    '🏌️‍♀️',
    '⚽️',
    '🏀',
    '🏈',
    '⚾️',
    '🎾',
    '🏐',
    '🏓',
    '🏸',
    '💪',
    '🦵',
    '🤲',
    '👐',
    '🙌',
    '👏',
    '🤝',
    '👍',
  ];

  @override
  void initState() {
    super.initState();
    // 初始化表单数据
    _nameController.text = widget.category.name;
    _descriptionController.text = widget.category.description ?? '';
    _selectedIcon = widget.category.icon;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      title: Row(
        children: [
          Icon(Icons.edit_outlined, color: const Color(0xFF2F76DA), size: 24),
          const SizedBox(width: 8),
          const Text('编辑分类'),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 错误信息显示
              if (_errorMessage != null) ...[
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.red.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.error_outline, color: Colors.red, size: 16),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: const TextStyle(
                            color: Colors.red,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // 分类名称输入
              const Text(
                '分类名称',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF37352F),
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _nameController,
                decoration: InputDecoration(
                  hintText: '请输入分类名称',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: const Color(0xFFE3E2E0)),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: const Color(0xFFE3E2E0)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: const Color(0xFF2F76DA)),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 12,
                  ),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '请输入分类名称';
                  }
                  // 检查名称是否已存在（排除当前分类）
                  if (widget.customCategoryManager.isCategoryNameExists(
                    value.trim(),
                    excludeId: widget.category.id,
                  )) {
                    return '分类名称已存在';
                  }
                  return null;
                },
                onFieldSubmitted: (_) => _saveCategory(),
              ),
              const SizedBox(height: 16),

              // 分类描述输入
              const Text(
                '分类描述',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF37352F),
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _descriptionController,
                maxLines: 3,
                decoration: InputDecoration(
                  hintText: '请输入分类描述（可选）',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: const Color(0xFFE3E2E0)),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: const Color(0xFFE3E2E0)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: const Color(0xFF2F76DA)),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 12,
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // 图标选择
              const Text(
                '选择图标',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF37352F),
                ),
              ),
              const SizedBox(height: 8),
              Container(
                height: 120,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border.all(color: const Color(0xFFE3E2E0)),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: Wrap(
                    spacing: 8,
                    runSpacing: 4,
                    alignment: WrapAlignment.start,
                    children: _availableIcons.map((icon) {
                      final isSelected = icon == _selectedIcon;

                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedIcon = icon;
                          });
                        },
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: isSelected
                                ? const Color(0xFF2F76DA).withValues(alpha: 0.1)
                                : Colors.transparent,
                            border: Border.all(
                              color: isSelected
                                  ? const Color(0xFF2F76DA)
                                  : const Color(0xFFE3E2E0),
                              width: isSelected ? 2 : 1,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Center(
                            child: Text(
                              icon,
                              style: const TextStyle(fontSize: 20),
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        TextButton(
          onPressed: _isLoading ? null : _saveCategory,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('保存'),
        ),
      ],
    );
  }

  /// 保存分类
  Future<void> _saveCategory() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final name = _nameController.text.trim();
      final description = _descriptionController.text.trim();

      // 创建更新后的分类
      final updatedCategory = widget.category.copyWith(
        name: name,
        icon: _selectedIcon,
        description: description.isEmpty ? null : description,
        lastModified: DateTime.now(),
      );

      // 更新分类
      await widget.customCategoryManager.updateCategory(updatedCategory);

      // 调用回调函数
      widget.onCategoryUpdated(updatedCategory);

      // 关闭对话框
      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      setState(() {
        _errorMessage = '更新分类失败：${e.toString()}';
        _isLoading = false;
      });
    }
  }
}

/// 分类选择对话框（复用知忆相册的分类选择逻辑）
class _CategorySelectionDialog extends StatefulWidget {
  final ActionLibraryCategoryManager categoryManager;
  final String currentCategory;
  final String libraryTitle;

  const _CategorySelectionDialog({
    required this.categoryManager,
    required this.currentCategory,
    required this.libraryTitle,
  });

  @override
  State<_CategorySelectionDialog> createState() =>
      _CategorySelectionDialogState();
}

class _CategorySelectionDialogState extends State<_CategorySelectionDialog> {
  String? _selectedCategory;
  List<String> _availableCategories = [];

  @override
  void initState() {
    super.initState();
    _availableCategories = widget.categoryManager.getAllCategoryNames();

    // 确保当前分类在可用分类列表中，如果不在则设为null
    if (_availableCategories.contains(widget.currentCategory)) {
      _selectedCategory = widget.currentCategory;
    } else {
      _selectedCategory = _availableCategories.isNotEmpty
          ? _availableCategories.first
          : null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      title: Row(
        children: [
          const Icon(Icons.category, color: Color(0xFF7C3AED)),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '更改分类',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
          ),
        ],
      ),
      content: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: 400,
          maxHeight: MediaQuery.of(context).size.height * 0.6,
        ),
        child: SizedBox(
          width: MediaQuery.of(context).size.width * 0.8,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '为动作库"${widget.libraryTitle}"选择新的分类：',
                style: const TextStyle(fontSize: 14, color: Color(0xFF9B9A97)),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedCategory,
                decoration: const InputDecoration(
                  labelText: '分类',
                  border: OutlineInputBorder(),
                ),
                dropdownColor: Colors.white,
                items: _availableCategories
                    .map(
                      (category) => DropdownMenuItem(
                        value: category,
                        child: Text(category),
                      ),
                    )
                    .toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    _selectedCategory = newValue;
                  });
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消', style: TextStyle(color: Color(0xFF9B9A97))),
        ),
        ElevatedButton(
          onPressed: _selectedCategory != null
              ? () => Navigator.of(context).pop(_selectedCategory)
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF7C3AED),
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            elevation: 0,
          ),
          child: const Text('确定'),
        ),
      ],
    );
  }
}

/// 通用分类创建对话框（支持自定义分类和系统默认分类）
class _AddCustomCategoryDialog extends StatefulWidget {
  final CustomExerciseCategoryManager? customCategoryManager;
  final ActionLibraryCategoryManager? treeCategoryManager;
  final Function(dynamic)? onCategoryAdded;
  final VoidCallback? onCategoriesChanged;
  final CustomExerciseCategory? parentCustomCategory; // 自定义分类的父分类
  final ActionLibraryCategoryNode? parentTreeCategory; // 系统分类的父分类

  const _AddCustomCategoryDialog({
    this.customCategoryManager,
    this.treeCategoryManager,
    this.onCategoryAdded,
    this.onCategoriesChanged,
    this.parentCustomCategory,
    this.parentTreeCategory,
  });

  @override
  State<_AddCustomCategoryDialog> createState() =>
      _AddCustomCategoryDialogState();
}

class _AddCustomCategoryDialogState extends State<_AddCustomCategoryDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  String _selectedIcon = '🏃‍♂️'; // 默认图标
  bool _isLoading = false;
  String? _errorMessage;

  // 辅助方法：判断是否为自定义分类模式
  bool get _isCustomCategoryMode => widget.customCategoryManager != null;

  // 辅助方法：判断是否为子分类创建
  bool get _isChildCategory =>
      widget.parentCustomCategory != null || widget.parentTreeCategory != null;

  // 辅助方法：获取父分类名称
  String? get _parentCategoryName {
    if (widget.parentCustomCategory != null) {
      return widget.parentCustomCategory!.name;
    }
    if (widget.parentTreeCategory != null) {
      return widget.parentTreeCategory!.title;
    }
    return null;
  }

  // 可选择的图标
  final List<String> _availableIcons = [
    '🏃‍♂️',
    '🤸‍♂️',
    '🏋️‍♂️',
    '🧘‍♂️',
    '🤾‍♂️',
    '🏊‍♂️',
    '🚴‍♂️',
    '🤺',
    '🥊',
    '🥋',
    '🏸',
    '🏓',
    '🎾',
    '🏐',
    '🏈',
    '⚽',
    '🏀',
    '🎯',
    '🎪',
    '🎭',
    '🎨',
    '🎵',
    '📚',
    '💡',
    '⭐',
    '🔥',
    '💪',
    '🎖️',
    '🏆',
    '🎊',
    '🌟',
    '✨',
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: const Color(0xFF2F76DA).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _isChildCategory
                  ? Icons.create_new_folder_outlined
                  : Icons.add_circle_outline,
              color: const Color(0xFF2F76DA),
              size: 18,
            ),
          ),
          const SizedBox(width: 12),
          Text(
            _isChildCategory && _parentCategoryName != null
                ? '为"$_parentCategoryName"添加子分类'
                : _isCustomCategoryMode
                ? '添加自定义分类'
                : '添加分类',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
        ],
      ),
      backgroundColor: Colors.white, // 确保使用纯白色背景
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ), // 使用12px圆角
      contentPadding: const EdgeInsets.fromLTRB(
        24,
        16,
        24,
        0,
      ), // 增加左右内边距以符合Notion风格
      content: SizedBox(
        width: double.maxFinite,
        child: SingleChildScrollView(
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 错误消息
                if (_errorMessage != null) ...[
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: Colors.red.shade200),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: Colors.red.shade600,
                          size: 16,
                        ),
                        const SizedBox(width: 6),
                        Expanded(
                          child: Text(
                            _errorMessage!,
                            style: TextStyle(
                              color: Colors.red.shade600,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 12),
                ],

                // 分类名称输入
                Text(
                  _isChildCategory ? '子分类名称 *' : '分类名称 *',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF37352F),
                  ),
                ),
                const SizedBox(height: 6),
                TextFormField(
                  controller: _nameController,
                  decoration: InputDecoration(
                    hintText: _isChildCategory ? '请输入子分类名称' : '请输入分类名称',
                    hintStyle: const TextStyle(color: Color(0xFF9B9A97)),
                    filled: true,
                    fillColor: Colors.white, // 使用纯白色背景
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: const BorderSide(color: Color(0xFF2F76DA)),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 10,
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return '请输入分类名称';
                    }
                    // 只有自定义分类模式才检查重名
                    if (_isCustomCategoryMode &&
                        widget.customCategoryManager!.isCategoryNameExists(
                          value.trim(),
                        )) {
                      return '分类名称已存在';
                    }
                    return null;
                  },
                  onChanged: (_) {
                    if (_errorMessage != null) {
                      setState(() {
                        _errorMessage = null;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),

                // 分类描述输入（仅自定义分类模式显示）
                if (_isCustomCategoryMode) ...[
                  Text(
                    _isChildCategory ? '子分类描述' : '分类描述',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF37352F),
                    ),
                  ),
                  const SizedBox(height: 6),
                  TextFormField(
                    controller: _descriptionController,
                    maxLines: 2,
                    decoration: InputDecoration(
                      hintText: _isChildCategory
                          ? '请输入子分类描述（可选）'
                          : '请输入分类描述（可选）',
                      hintStyle: const TextStyle(color: Color(0xFF9B9A97)),
                      filled: true,
                      fillColor: Colors.white, // 使用纯白色背景
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(color: Color(0xFF2F76DA)),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 10,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],

                // 图标选择
                const Text(
                  '选择图标',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF37352F),
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  constraints: const BoxConstraints(
                    minHeight: 120,
                    maxHeight: 160,
                  ),
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF7F6F3),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: SingleChildScrollView(
                    child: Wrap(
                      spacing: 4,
                      runSpacing: 4,
                      alignment: WrapAlignment.start,
                      children: _availableIcons.map((icon) {
                        final isSelected = icon == _selectedIcon;

                        return GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedIcon = icon;
                            });
                          },
                          child: Container(
                            width: 32,
                            height: 32,
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? const Color(
                                      0xFF2F76DA,
                                    ).withValues(alpha: 0.1)
                                  : Colors.transparent,
                              borderRadius: BorderRadius.circular(6),
                              border: isSelected
                                  ? Border.all(
                                      color: const Color(0xFF2F76DA),
                                      width: 2,
                                    )
                                  : null,
                            ),
                            child: Center(
                              child: Text(
                                icon,
                                style: const TextStyle(fontSize: 18),
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ),

                // 添加图标选择器与操作按钮之间的间距
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ),
      actionsPadding: const EdgeInsets.fromLTRB(20, 8, 20, 16),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.pop(context),
          style: TextButton.styleFrom(
            backgroundColor: Colors.white, // 白色背景
            padding: const EdgeInsets.symmetric(
              horizontal: 20,
              vertical: 12,
            ), // 调整内边距
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12), // 使用12px圆角
              side: const BorderSide(color: Color(0xFFE3E2E0)), // 添加边框
            ),
          ),
          child: const Text(
            '取消',
            style: TextStyle(
              color: Color(0xFF9B9A97), // 使用统一的灰色
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const SizedBox(width: 12), // 添加按钮间距
        ElevatedButton(
          onPressed: _isLoading ? null : _createCategory,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF2F76DA),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(
              horizontal: 20,
              vertical: 12,
            ), // 调整内边距
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12), // 使用12px圆角
            ),
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text(
                  '创建',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
        ),
      ],
    );
  }

  /// 创建分类
  Future<void> _createCategory() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final name = _nameController.text.trim();
    final description = _descriptionController.text.trim();

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      if (_isCustomCategoryMode) {
        // 自定义分类模式
        final newCategory = CustomExerciseCategory(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          name: name,
          icon: _selectedIcon,
          description: description.isEmpty ? null : description,
          createdAt: DateTime.now(),
          lastModified: DateTime.now(),
          parentId: widget.parentCustomCategory?.id,
          level: widget.parentCustomCategory != null
              ? widget.parentCustomCategory!.level + 1
              : 0,
        );

        if (widget.parentCustomCategory != null) {
          // 创建子分类
          await widget.customCategoryManager!.addChildCategory(
            widget.parentCustomCategory!.id,
            newCategory,
          );
        } else {
          // 创建根分类
          await widget.customCategoryManager!.addCategory(newCategory);
        }

        // 调用回调函数
        widget.onCategoryAdded?.call(newCategory);
      } else {
        // 系统分类模式
        if (widget.parentTreeCategory != null) {
          // 创建子分类
          widget.treeCategoryManager!.addNode(
            name,
            icon: _selectedIcon,
            parent: widget.parentTreeCategory,
          );
        } else {
          // 创建根分类
          widget.treeCategoryManager!.addNode(name, icon: _selectedIcon);
        }

        // 调用回调函数
        widget.onCategoriesChanged?.call();
      }

      // 关闭对话框
      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      setState(() {
        _errorMessage =
            '创建${_isChildCategory ? '子分类' : '分类'}失败：${e.toString()}';
        _isLoading = false;
      });
    }
  }
}
