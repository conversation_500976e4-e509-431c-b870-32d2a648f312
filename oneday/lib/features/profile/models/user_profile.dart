import 'package:json_annotation/json_annotation.dart';

part 'user_profile.g.dart';

/// 用户个人资料模型
@JsonSerializable()
class UserProfile {
  /// 用户ID
  final String userId;

  /// 用户昵称
  final String nickname;

  /// 个人简介
  final String? bio;

  /// 头像路径（本地文件路径）
  final String? avatarPath;

  /// 创建时间
  final DateTime createdAt;

  /// 最后更新时间
  final DateTime updatedAt;

  const UserProfile({
    required this.userId,
    required this.nickname,
    this.bio,
    this.avatarPath,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) =>
      _$UserProfileFromJson(json);

  Map<String, dynamic> toJson() => _$UserProfileToJson(this);

  /// 创建默认用户资料
  factory UserProfile.defaultProfile(String userId) {
    final now = DateTime.now();
    return UserProfile(
      userId: userId,
      nickname: '学习者',
      bio: '让每一天都充满收获',
      avatarPath: null,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// 复制并更新用户资料
  UserProfile copyWith({
    String? userId,
    String? nickname,
    String? bio,
    String? avatarPath,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool clearBio = false,
    bool clearAvatarPath = false,
  }) {
    return UserProfile(
      userId: userId ?? this.userId,
      nickname: nickname ?? this.nickname,
      bio: clearBio ? null : (bio ?? this.bio),
      avatarPath: clearAvatarPath ? null : (avatarPath ?? this.avatarPath),
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// 获取显示用的头像
  String get displayAvatar {
    return avatarPath ?? '';
  }

  /// 获取显示用的昵称
  String get displayNickname {
    return nickname.isNotEmpty ? nickname : '学习者';
  }

  /// 获取显示用的个人简介
  String get displayBio {
    return bio?.isNotEmpty == true ? bio! : '让每一天都充满收获';
  }

  /// 验证昵称是否有效
  static bool isValidNickname(String nickname) {
    return nickname.trim().isNotEmpty && nickname.trim().length <= 20;
  }

  /// 验证个人简介是否有效
  static bool isValidBio(String? bio) {
    if (bio == null || bio.trim().isEmpty) return true;
    return bio.trim().length <= 100;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserProfile &&
          runtimeType == other.runtimeType &&
          userId == other.userId &&
          nickname == other.nickname &&
          bio == other.bio &&
          avatarPath == other.avatarPath;

  @override
  int get hashCode =>
      userId.hashCode ^ nickname.hashCode ^ bio.hashCode ^ avatarPath.hashCode;

  @override
  String toString() {
    return 'UserProfile{userId: $userId, nickname: $nickname, bio: $bio, avatarPath: $avatarPath}';
  }
}
