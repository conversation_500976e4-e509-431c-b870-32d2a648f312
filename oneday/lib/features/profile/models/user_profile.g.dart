// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_profile.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserProfile _$UserProfileFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('UserProfile', json, ($checkedConvert) {
  final val = UserProfile(
    userId: $checkedConvert('userId', (v) => v as String),
    nickname: $checkedConvert('nickname', (v) => v as String),
    bio: $checkedConvert('bio', (v) => v as String?),
    avatarPath: $checkedConvert('avatarPath', (v) => v as String?),
    createdAt: $checkedConvert('createdAt', (v) => DateTime.parse(v as String)),
    updatedAt: $checkedConvert('updatedAt', (v) => DateTime.parse(v as String)),
  );
  return val;
});

// ignore: unused_element
abstract class _$UserProfilePerFieldToJson {
  // ignore: unused_element
  static Object? userId(String instance) => instance;
  // ignore: unused_element
  static Object? nickname(String instance) => instance;
  // ignore: unused_element
  static Object? bio(String? instance) => instance;
  // ignore: unused_element
  static Object? avatarPath(String? instance) => instance;
  // ignore: unused_element
  static Object? createdAt(DateTime instance) => instance.toIso8601String();
  // ignore: unused_element
  static Object? updatedAt(DateTime instance) => instance.toIso8601String();
}

Map<String, dynamic> _$UserProfileToJson(UserProfile instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'nickname': instance.nickname,
      'bio': instance.bio,
      'avatarPath': instance.avatarPath,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };
