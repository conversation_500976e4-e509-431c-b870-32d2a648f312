import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../services/first_time_service.dart';
import '../study_time/providers/study_time_providers.dart';
import '../memory_palace/providers/memory_palace_provider.dart';
import '../time_box/providers/timebox_provider.dart';
import '../../debug/profanity_filter_manual_test_page.dart';
import '../../debug/image_picker_debug_page.dart';
import '../../debug/image_cropper_test_page.dart';
import '../../debug/profile_save_test_page.dart';
import 'providers/user_profile_provider.dart';

/// 个人中心页面
///
/// 提供用户设置、个人信息管理、应用配置等功能
/// 采用Notion风格，简洁清晰的卡片式布局
class ProfilePage extends ConsumerStatefulWidget {
  const ProfilePage({super.key});

  @override
  ConsumerState<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends ConsumerState<ProfilePage> {
  @override
  void initState() {
    super.initState();
    // 初始化时刷新数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshStudyData();
    });
  }

  /// 刷新学习数据
  Future<void> _refreshStudyData() async {
    try {
      // 刷新时间盒子数据
      await ref.read(timeBoxProvider.notifier).refresh();

      // 刷新记忆宫殿数据
      await ref.read(memoryPalaceProvider.notifier).refresh();

      // 显示刷新成功提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('数据已刷新'),
            duration: Duration(seconds: 1),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      // 显示刷新失败提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('刷新失败: $e'),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F6F3),
      appBar: AppBar(
        automaticallyImplyLeading: false,
        backgroundColor: Colors.white,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        centerTitle: false,
        title: const Text(
          '我的',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 22,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () => context.push('/settings'),
            icon: const Icon(Icons.settings_outlined, color: Color(0xFF37352F)),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 用户信息卡片
            _buildUserCard(context),

            const SizedBox(height: 24),

            // 学习统计
            _buildStatsSection(context),

            const SizedBox(height: 24),

            // 功能菜单
            _buildMenuSection(context),

            const SizedBox(height: 24),

            // 其他设置
            _buildOtherSection(context),
          ],
        ),
      ),
    );
  }

  /// 构建用户信息卡片
  Widget _buildUserCard(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        final userProfile = ref.watch(currentUserProfileProvider);

        return GestureDetector(
          onTap: _onAvatarTap,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF37352F).withValues(alpha: 0.1),
              ),
            ),
            child: Row(
              children: [
                // 头像
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(30),
                    border: Border.all(
                      color: const Color(0xFF37352F).withValues(alpha: 0.1),
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(28),
                    child: userProfile?.avatarPath != null
                        ? Image.file(
                            File(userProfile!.avatarPath!),
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return _buildDefaultAvatar();
                            },
                          )
                        : _buildDefaultAvatar(),
                  ),
                ),

                const SizedBox(width: 16),

                // 用户信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        userProfile?.displayNickname ?? '学习者',
                        style: const TextStyle(
                          color: Color(0xFF37352F),
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        userProfile?.displayBio ?? '让每一天都充满收获',
                        style: TextStyle(
                          color: const Color(0xFF37352F).withValues(alpha: 0.7),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),

                // 编辑按钮
                IconButton(
                  onPressed: () => _editProfile(context),
                  icon: const Icon(
                    Icons.edit_outlined,
                    color: Color(0xFF9B9A97),
                    size: 20,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 构建默认头像
  Widget _buildDefaultAvatar() {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(28),
      ),
      child: const Icon(Icons.person, size: 30, color: Color(0xFF2E7EED)),
    );
  }

  /// 构建学习统计区域
  Widget _buildStatsSection(BuildContext context) {
    // 监听学习统计数据
    final todayStudySummary = ref.watch(todayStudySummaryProvider);
    final memoryPalaceStats = ref.watch(memoryPalaceStatsProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '学习概览',
              style: TextStyle(
                color: Color(0xFF37352F),
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            // 添加刷新按钮
            IconButton(
              onPressed: _refreshStudyData,
              icon: const Icon(
                Icons.refresh,
                color: Color(0xFF9B9A97),
                size: 20,
              ),
              tooltip: '刷新数据',
            ),
          ],
        ),

        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                '今日学习',
                todayStudySummary['studyTime'] ?? '0m',
                Icons.timer_outlined,
                const Color(0xFF2E7EED),
              ),
            ),

            const SizedBox(width: 12),

            Expanded(
              child: _buildStatCard(
                '累计工资',
                todayStudySummary['wage'] ?? '¥0',
                Icons.account_balance_wallet_outlined,
                const Color(0xFF0F7B6C),
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                '连续天数',
                todayStudySummary['streakDays'] ?? '0天',
                Icons.local_fire_department_outlined,
                const Color(0xFFE03E3E),
              ),
            ),

            const SizedBox(width: 12),

            Expanded(
              child: _buildStatCard(
                '记忆宫殿',
                memoryPalaceStats['formattedCount'] ?? '0个',
                Icons.psychology_outlined,
                const Color(0xFF7C3AED),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建统计卡片
  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Semantics(
      label: '$title: $value',
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: const Color(0xFF37352F).withValues(alpha: 0.1),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, size: 18, color: color),
                const SizedBox(width: 6),
                Text(
                  title,
                  style: TextStyle(
                    color: const Color(0xFF37352F).withValues(alpha: 0.7),
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            Text(
              value,
              style: const TextStyle(
                color: Color(0xFF37352F),
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建功能菜单区域
  Widget _buildMenuSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '功能菜单',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: 12),

        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFF37352F).withValues(alpha: 0.1),
            ),
          ),
          child: Column(
            children: [
              _buildMenuItem(
                icon: Icons.book_outlined,
                title: '优化日志',
                subtitle: '记录学习反思和成长历程',
                color: const Color(0xFF2E7EED),
                onTap: () => context.push('/reflection'),
              ),

              _buildDivider(),

              _buildMenuItem(
                icon: Icons.radar,
                title: '能力雷达图',
                subtitle: '查看个人五维能力分析',
                color: const Color(0xFF7C3AED),
                onTap: () => context.push('/ability-radar'),
              ),

              _buildDivider(),

              _buildMenuItem(
                icon: Icons.analytics_outlined,
                title: '学习报告',
                subtitle: '查看详细的学习分析',
                color: const Color(0xFF0F7B6C),
                onTap: () => context.push('/learning-report'),
              ),

              _buildDivider(),

              _buildMenuItem(
                icon: Icons.emoji_events_outlined,
                title: '成就系统',
                subtitle: '解锁学习成就和徽章',
                color: const Color(0xFFFFD700),
                onTap: () => context.push('/achievement'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建其他设置区域
  Widget _buildOtherSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '其他',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: 12),

        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFF37352F).withValues(alpha: 0.1),
            ),
          ),
          child: Column(
            children: [
              _buildMenuItem(
                icon: Icons.help_outline,
                title: '帮助与反馈',
                subtitle: '使用指南和意见反馈',
                color: const Color(0xFF9B9A97),
                onTap: () => context.push('/help-feedback'),
              ),

              _buildDivider(),

              _buildMenuItem(
                icon: Icons.info_outline,
                title: '关于OneDay',
                subtitle: '版本信息和开发团队',
                color: const Color(0xFF9B9A97),
                onTap: () => _showAbout(context),
              ),

              // 开发者测试按钮（仅在调试模式下显示）
              if (kDebugMode) ...[
                _buildDivider(),
                _buildMenuItem(
                  icon: Icons.developer_mode,
                  title: '开发者工具',
                  subtitle: '测试功能和开发者选项',
                  color: const Color(0xFFD9730D),
                  onTap: _showDeveloperTestDialog,
                ),
                _buildMenuItem(
                  icon: Icons.sync,
                  title: '数据同步测试',
                  subtitle: '测试TimeBox数据同步',
                  color: const Color(0xFF2E7EED),
                  onTap: () => context.push('/test-data-sync'),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  /// 构建菜单项
  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Semantics(
      label: '$title: $subtitle',
      button: true,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(icon, color: color, size: 20),
                ),

                const SizedBox(width: 16),

                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          color: Color(0xFF37352F),
                          fontSize: 15,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        subtitle,
                        style: TextStyle(
                          color: const Color(0xFF37352F).withValues(alpha: 0.6),
                          fontSize: 13,
                        ),
                      ),
                    ],
                  ),
                ),

                Icon(
                  Icons.chevron_right,
                  color: const Color(0xFF9B9A97).withValues(alpha: 0.7),
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建分割线
  Widget _buildDivider() {
    return Container(
      margin: const EdgeInsets.only(left: 72),
      height: 1,
      color: const Color(0xFF37352F).withValues(alpha: 0.06),
    );
  }

  /// 编辑个人资料
  void _editProfile(BuildContext context) {
    // 使用GoRouter导航到个人资料编辑页面，这样会隐藏底部导航栏
    context.push('/profile-edit');
  }

  /// 显示关于页面
  void _showAbout(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('关于OneDay'),
        content: const Text(
          'OneDay - 让每一天都充满收获\n\n'
          '版本：1.0.0\n'
          '开发团队：OneDay团队\n\n'
          '一个将游戏化激励、记忆科学与健康管理深度融合的学习平台。',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 头像点击处理 - 直接跳转登录页面
  void _onAvatarTap() async {
    context.push('/login');
  }

  /// 显示开发者测试对话框
  void _showDeveloperTestDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: Row(
          children: [
            Icon(
              Icons.developer_mode,
              color: const Color(0xFFD9730D),
              size: 24,
            ),
            const SizedBox(width: 8),
            const Text(
              '开发者工具',
              style: TextStyle(
                color: Color(0xFF37352F),
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '选择要执行的开发者操作：',
              style: TextStyle(color: Color(0xFF787774), fontSize: 14),
            ),
            const SizedBox(height: 16),
            _buildDeveloperOption(
              icon: Icons.refresh,
              title: '重置引导页',
              description: '恢复首次使用状态，重启后显示引导页',
              onTap: () => _resetOnboarding(context),
            ),
            const SizedBox(height: 12),
            _buildDeveloperOption(
              icon: Icons.clear_all,
              title: '清除所有数据',
              description: '清除应用的所有本地存储数据',
              onTap: () => _clearAllData(context),
            ),
            const SizedBox(height: 12),
            _buildDeveloperOption(
              icon: Icons.bug_report,
              title: '调试信息',
              description: '显示应用的调试和版本信息',
              onTap: () => _showDebugInfo(context),
            ),
            const SizedBox(height: 12),
            _buildDeveloperOption(
              icon: Icons.route,
              title: '路由调试',
              description: '测试路由配置和导航功能',
              onTap: () => _openRouteDebug(context),
            ),
            const SizedBox(height: 12),
            _buildDeveloperOption(
              icon: Icons.security,
              title: '敏感词过滤测试',
              description: '测试社区发帖敏感词过滤功能',
              onTap: () => _openProfanityFilterTest(context),
            ),
            const SizedBox(height: 12),
            _buildDeveloperOption(
              icon: Icons.image,
              title: '图片选择调试',
              description: '测试图片选择和权限功能',
              onTap: () => _openImagePickerDebug(context),
            ),
            const SizedBox(height: 12),
            _buildDeveloperOption(
              icon: Icons.crop,
              title: '图片裁剪测试',
              description: '专门测试图片裁剪功能',
              onTap: () => _openImageCropperTest(context),
            ),
            const SizedBox(height: 12),
            _buildDeveloperOption(
              icon: Icons.save,
              title: '资料保存测试',
              description: '测试个人资料保存功能',
              onTap: () => _openProfileSaveTest(context),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              foregroundColor: const Color(0xFF9B9A97),
            ),
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }

  /// 构建开发者选项
  Widget _buildDeveloperOption({
    required IconData icon,
    required String title,
    required String description,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(
            color: const Color(0xFF37352F).withValues(alpha: 0.1),
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: const Color(0xFFD9730D).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, size: 18, color: const Color(0xFFD9730D)),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      color: Color(0xFF37352F),
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    description,
                    style: const TextStyle(
                      color: Color(0xFF787774),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              size: 14,
              color: Color(0xFF9B9A97),
            ),
          ],
        ),
      ),
    );
  }

  /// 重置引导页
  void _resetOnboarding(BuildContext context) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    Navigator.of(context).pop();
    await FirstTimeService.instance.resetFirstTimeStatus();
    if (mounted) {
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('✅ 已重置首次使用状态，重启应用后将显示引导页'),
          backgroundColor: Color(0xFF0F7B6C),
          duration: Duration(seconds: 3),
        ),
      );
    }
  }

  /// 清除所有数据
  void _clearAllData(BuildContext context) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Text(
          '⚠️ 确认清除数据',
          style: TextStyle(
            color: Color(0xFFE03E3E),
            fontWeight: FontWeight.bold,
          ),
        ),
        content: const Text(
          '此操作将清除所有本地存储数据，包括用户设置、学习记录等。此操作不可撤销，确定要继续吗？',
          style: TextStyle(color: Color(0xFF787774)),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: const Color(0xFFE03E3E),
            ),
            child: const Text('确认清除'),
          ),
        ],
      ),
    );

    if (confirmed == true && context.mounted) {
      final scaffoldMessenger = ScaffoldMessenger.of(context);
      Navigator.of(context).pop();

      // 执行异步操作
      await FirstTimeService.instance.clearAll();

      if (mounted) {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('✅ 已清除所有本地数据'),
            backgroundColor: Color(0xFF0F7B6C),
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// 显示调试信息
  void _showDebugInfo(BuildContext context) {
    Navigator.of(context).pop();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Text(
          '🐛 调试信息',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDebugInfoRow('应用版本', '1.0.0+1'),
            _buildDebugInfoRow('Flutter版本', '3.x.x'),
            _buildDebugInfoRow('构建模式', kDebugMode ? 'Debug' : 'Release'),
            _buildDebugInfoRow('平台', Theme.of(context).platform.name),
            const SizedBox(height: 8),
            const Divider(),
            const SizedBox(height: 8),
            const Text(
              '开发者：OneDay团队',
              style: TextStyle(color: Color(0xFF787774), fontSize: 12),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  /// 构建调试信息行
  Widget _buildDebugInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Color(0xFF37352F),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: const TextStyle(color: Color(0xFF787774), fontSize: 14),
          ),
        ],
      ),
    );
  }

  /// 打开路由调试页面
  void _openRouteDebug(BuildContext context) {
    Navigator.of(context).pop(); // 关闭开发者对话框
    context.push('/route-debug');
  }

  /// 打开敏感词过滤测试页面
  void _openProfanityFilterTest(BuildContext context) {
    Navigator.of(context).pop(); // 关闭开发者对话框
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const ProfanityFilterManualTestPage(),
      ),
    );
  }

  /// 打开图片选择调试页面
  void _openImagePickerDebug(BuildContext context) {
    Navigator.of(context).pop(); // 关闭对话框
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const ImagePickerDebugPage()),
    );
  }

  /// 打开图片裁剪测试页面
  void _openImageCropperTest(BuildContext context) {
    Navigator.of(context).pop(); // 关闭对话框
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const ImageCropperTestPage()),
    );
  }

  /// 打开个人资料保存测试页面
  void _openProfileSaveTest(BuildContext context) {
    Navigator.of(context).pop(); // 关闭对话框
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const ProfileSaveTestPage()),
    );
  }
}
