import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'vocabulary_service.dart';
import 'vocabulary_learning_service.dart';
import 'word_model.dart';
import '../exercise/exercise_session_page.dart';
import '../achievement/providers/achievement_provider.dart';

/// 分类词汇列表页面
class VocabularyCategoryPage extends ConsumerStatefulWidget {
  final VocabularyCategory category;

  const VocabularyCategoryPage({super.key, required this.category});

  @override
  ConsumerState<VocabularyCategoryPage> createState() =>
      _VocabularyCategoryPageState();
}

class _VocabularyCategoryPageState
    extends ConsumerState<VocabularyCategoryPage> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  Set<String> _selectedWords = {};
  bool _isSelectionMode = false;
  String _sortBy = 'frequency'; // frequency, alphabetical, difficulty
  bool _sortAscending = false; // 默认按频率降序
  String _filterDifficulty = 'all';
  String _filterPartOfSpeech = 'all';
  bool _isAddingToMemory = false; // 是否正在添加到记忆词库

  @override
  void initState() {
    super.initState();
    _loadSelectedWords();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// 加载已选择的单词
  void _loadSelectedWords() async {
    final vocabularyService = ref.read(vocabularyServiceProvider);
    final selectedWords = await vocabularyService.getSelectedWords(
      widget.category.id,
    );
    setState(() {
      _selectedWords = selectedWords;
    });
  }

  @override
  Widget build(BuildContext context) {
    final wordsAsync = ref.watch(categoryWordsProvider(widget.category.id));

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.category.name,
              style: const TextStyle(
                color: Color(0xFF37352F),
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              '${_selectedWords.length}/${widget.category.totalWords} 已选择',
              style: const TextStyle(color: Color(0xFF787774), fontSize: 12),
            ),
          ],
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF37352F)),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          if (_isSelectionMode) ...[
            TextButton(
              onPressed: _selectAll,
              child: const Text(
                '全选',
                style: TextStyle(color: Color(0xFF2F76DA)),
              ),
            ),
            TextButton(
              onPressed: _clearSelection,
              child: const Text(
                '清空',
                style: TextStyle(color: Color(0xFF787774)),
              ),
            ),
          ] else ...[
            IconButton(
              icon: const Icon(Icons.filter_list, color: Color(0xFF787774)),
              onPressed: _showFilterOptions,
            ),
            IconButton(
              icon: const Icon(Icons.sort, color: Color(0xFF787774)),
              onPressed: _showSortOptions,
            ),
          ],
          IconButton(
            icon: Icon(
              _isSelectionMode ? Icons.check : Icons.checklist,
              color: const Color(0xFF2F76DA),
            ),
            onPressed: _toggleSelectionMode,
          ),
        ],
      ),
      body: Column(
        children: [
          // 搜索栏
          Container(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: '搜索单词或释义...',
                hintStyle: const TextStyle(color: Color(0xFF9B9A97)),
                prefixIcon: const Icon(Icons.search, color: Color(0xFF9B9A97)),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear, color: Color(0xFF9B9A97)),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchQuery = '';
                          });
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFF2F76DA)),
                ),
                filled: true,
                fillColor: const Color(0xFFF7F6F3),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),

          // 单词列表
          Expanded(
            child: wordsAsync.when(
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 48,
                      color: Colors.grey,
                    ),
                    const SizedBox(height: 16),
                    Text('加载失败: $error'),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => ref.refresh(
                        categoryWordsProvider(widget.category.id),
                      ),
                      child: const Text('重试'),
                    ),
                  ],
                ),
              ),
              data: (words) {
                // 过滤和排序结果
                final filteredWords = _filterAndSortWords(words);

                if (filteredWords.isEmpty) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.search_off, size: 48, color: Colors.grey),
                        SizedBox(height: 16),
                        Text('没有找到匹配的单词', style: TextStyle(color: Colors.grey)),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: filteredWords.length,
                  itemBuilder: (context, index) {
                    final entry = filteredWords[index];
                    final word = entry.key;
                    final details = entry.value;
                    final isSelected = _selectedWords.contains(word);

                    return _WordListItem(
                      word: word,
                      details: details,
                      isSelected: isSelected,
                      isSelectionMode: _isSelectionMode,
                      onTap: () => _handleWordTap(word),
                      onSelectionChanged: (selected) =>
                          _handleSelectionChanged(word, selected ?? false),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
      bottomNavigationBar: _selectedWords.isNotEmpty
          ? Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Colors.white,
                border: Border(top: BorderSide(color: Color(0xFFE3E2E0))),
              ),
              child: SafeArea(
                child: ElevatedButton(
                  onPressed: _isAddingToMemory ? null : _addToMemory,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF2F76DA),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: _isAddingToMemory
                      ? const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            ),
                            SizedBox(width: 8),
                            Text(
                              '正在添加...',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        )
                      : Text(
                          '加入记忆 (${_selectedWords.length}个单词)',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ),
            )
          : null,
    );
  }

  /// 处理单词点击
  void _handleWordTap(String word) {
    if (_isSelectionMode) {
      _handleSelectionChanged(word, !_selectedWords.contains(word));
    } else {
      _showWordDetails(word);
    }
  }

  /// 处理选择状态变化
  void _handleSelectionChanged(String word, bool selected) {
    setState(() {
      if (selected) {
        _selectedWords.add(word);
      } else {
        _selectedWords.remove(word);
      }
    });
    _saveSelectedWords();
  }

  /// 保存选中的单词
  void _saveSelectedWords() async {
    final vocabularyService = ref.read(vocabularyServiceProvider);
    await vocabularyService.updateSelectedWords(
      widget.category.id,
      _selectedWords,
    );
  }

  /// 切换选择模式
  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
    });
  }

  /// 全选
  void _selectAll() async {
    final wordsAsync = ref.read(categoryWordsProvider(widget.category.id));
    wordsAsync.whenData((words) {
      setState(() {
        _selectedWords = words.map((e) => e.key).toSet();
      });
      _saveSelectedWords();
    });
  }

  /// 清空选择
  void _clearSelection() {
    setState(() {
      _selectedWords.clear();
    });
    _saveSelectedWords();
  }

  /// 显示单词详情
  void _showWordDetails(String word) async {
    final wordsAsync = ref.read(categoryWordsProvider(widget.category.id));

    await wordsAsync.when(
      loading: () async {
        // 显示加载状态
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('正在加载单词详情...')));
      },
      error: (error, stack) async {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('加载单词详情失败: $error'),
            backgroundColor: const Color(0xFFE03E3E),
          ),
        );
      },
      data: (allWords) async {
        // 查找对应的单词详情
        final wordEntry = allWords.firstWhere(
          (entry) => entry.key == word,
          orElse: () => MapEntry(
            word,
            WordDetails(
              id: 0,
              definition: '未找到定义',
              frequency: 0,
              difficulty: 'unknown',
              category: '',
              alternativeSpellings: [],
              partOfSpeech: 'unknown',
              examples: [],
              tags: [],
              priority: 'low',
            ),
          ),
        );

        if (mounted) {
          await showDialog(
            context: context,
            builder: (context) => _WordDetailDialog(
              word: word,
              details: wordEntry.value,
              onAddToMemory: () =>
                  _addSingleWordToMemory(word, wordEntry.value),
            ),
          );
        }
      },
    );
  }

  /// 添加单个单词到记忆词库
  Future<void> _addSingleWordToMemory(String word, WordDetails details) async {
    try {
      final vocabularyService = ref.read(vocabularyServiceProvider);

      // 检查是否已在记忆词库中
      final isInMemory = await vocabularyService.isInMemoryVocabulary(word);
      if (isInMemory) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('该单词已在记忆词库中'),
              backgroundColor: Color(0xFF787774),
            ),
          );
        }
        return;
      }

      // 添加到记忆词库
      await vocabularyService.addToMemoryVocabulary(
        [word],
        [MapEntry(word, details)],
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ 成功添加到记忆词库'),
            backgroundColor: Color(0xFF0F7B6C),
          ),
        );

        // 刷新记忆词库数据
        ref.invalidate(memoryVocabularyProvider);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('添加到记忆词库失败: $e'),
            backgroundColor: const Color(0xFFE03E3E),
          ),
        );
      }
    }
  }

  /// 加入记忆词库
  void _addToMemory() async {
    if (_selectedWords.isEmpty || _isAddingToMemory) return;

    setState(() {
      _isAddingToMemory = true;
    });

    try {
      final vocabularyService = ref.read(vocabularyServiceProvider);
      final wordsAsync = ref.read(categoryWordsProvider(widget.category.id));

      await wordsAsync.when(
        loading: () async {
          // 已经显示了加载状态，这里不需要额外处理
        },
        error: (error, stack) async {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('加载单词数据失败: $error'),
              backgroundColor: const Color(0xFFE03E3E),
            ),
          );
        },
        data: (allWords) async {
          // 过滤出选中的单词
          final selectedWordDetails = allWords
              .where((entry) => _selectedWords.contains(entry.key))
              .toList();

          if (selectedWordDetails.isEmpty) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('没有找到选中的单词'),
                backgroundColor: Color(0xFFE03E3E),
              ),
            );
            return;
          }

          // 检查是否有词汇已在记忆词库中
          final List<String> alreadyInMemory = [];
          final List<String> newWords = [];
          final List<MapEntry<String, WordDetails>> newWordDetails = [];

          for (final entry in selectedWordDetails) {
            final isInMemory = await vocabularyService.isInMemoryVocabulary(
              entry.key,
            );
            if (isInMemory) {
              alreadyInMemory.add(entry.key);
            } else {
              newWords.add(entry.key);
              newWordDetails.add(entry);
            }
          }

          // 添加新词汇到记忆词库
          if (newWords.isNotEmpty) {
            await vocabularyService.addToMemoryVocabulary(
              newWords,
              newWordDetails,
            );
          }

          // 显示结果反馈
          if (mounted) {
            String message;
            Color backgroundColor;

            if (newWords.isNotEmpty && alreadyInMemory.isEmpty) {
              // 全部成功添加
              message = '✅ 成功添加 ${newWords.length} 个单词到记忆词库';
              backgroundColor = const Color(0xFF0F7B6C);
            } else if (newWords.isNotEmpty && alreadyInMemory.isNotEmpty) {
              // 部分添加成功
              message =
                  '✅ 添加 ${newWords.length} 个新单词，${alreadyInMemory.length} 个已存在';
              backgroundColor = const Color(0xFF2F76DA);
            } else {
              // 全部已存在
              message = 'ℹ️ 所选单词已在记忆词库中';
              backgroundColor = const Color(0xFF787774);
            }

            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(message),
                backgroundColor: backgroundColor,
                duration: const Duration(seconds: 3),
              ),
            );

            // 刷新记忆词库数据
            ref.invalidate(memoryVocabularyProvider);
          }
        },
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('添加到记忆词库失败: $e'),
            backgroundColor: const Color(0xFFE03E3E),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isAddingToMemory = false;
        });
      }
    }
  }

  /// 处理学习结果
  Future<void> _handleLearningResult(
    Map<String, dynamic> result,
    Map<String, dynamic> exerciseData,
  ) async {
    final learningService = ref.read(vocabularyLearningServiceProvider);
    final word = exerciseData['paoWord'] as String;
    final session = exerciseData['session'] as VocabularyLearningSession;

    final completed = result['completed'] ?? false;
    final masteryScore = (result['masteryScore'] ?? 0.7).toDouble();

    // 更新学习进度
    await learningService.handleLearningCompletion(
      word: word,
      completed: completed,
      masteryScore: masteryScore,
      categoryId: widget.category.id,
    );

    if (completed) {
      // 触发学习单词成就
      ref.read(achievementProvider.notifier).onWordLearned();

      // 检查是否有下一个单词
      final nextWord = learningService.getNextWordInSession(session, word);

      if (nextWord != null && mounted) {
        // 询问是否继续学习下一个单词
        final shouldContinue = await _showContinueLearningDialog(nextWord);
        if (shouldContinue) {
          _startNextWordLearning(session, nextWord);
          return;
        }
      }

      // 显示学习完成消息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('单词 "$word" 学习完成！'),
            backgroundColor: const Color(0xFF0F7B6C),
          ),
        );
      }
    } else {
      // 学习未完成
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('单词 "$word" 学习未完成，建议稍后再试'),
            backgroundColor: const Color(0xFFD9730D),
          ),
        );
      }
    }

    // 刷新页面数据
    if (mounted) {
      ref.invalidate(categoryWordsProvider(widget.category.id));
      ref.invalidate(learningProgressProvider);
    }
  }

  /// 显示继续学习对话框
  Future<bool> _showContinueLearningDialog(String nextWord) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('继续学习'),
            content: Text('是否继续学习下一个单词："$nextWord"？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('稍后'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF2F76DA),
                  foregroundColor: Colors.white,
                ),
                child: const Text('继续'),
              ),
            ],
          ),
        ) ??
        false;
  }

  /// 开始下一个单词的学习
  void _startNextWordLearning(
    VocabularyLearningSession session,
    String nextWord,
  ) async {
    try {
      final learningService = ref.read(vocabularyLearningServiceProvider);

      // 创建只包含下一个单词的新会话
      final nextSession = VocabularyLearningSession(
        words: [nextWord],
        wordDetails: session.wordDetails
            .where((entry) => entry.key == nextWord)
            .toList(),
        sessionType: session.sessionType,
        createdAt: DateTime.now(),
      );

      final exerciseData = learningService.prepareExerciseSession(nextSession);

      // 导航到练习页面
      final result = await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ExerciseSessionPage(
            exercises: exerciseData['exercises'],
            paoWord: exerciseData['paoWord'],
            wordPhonetic: exerciseData['wordPhonetic'],
            wordMeaning: exerciseData['wordMeaning'],
            mode: exerciseData['mode'],
            duration: exerciseData['duration'],
          ),
        ),
      );

      // 递归处理结果
      if (result != null && mounted) {
        await _handleLearningResult(result, exerciseData);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('启动下一个单词学习失败: $e'),
            backgroundColor: const Color(0xFFE03E3E),
          ),
        );
      }
    }
  }

  /// 过滤和排序单词
  List<MapEntry<String, WordDetails>> _filterAndSortWords(
    List<MapEntry<String, WordDetails>> words,
  ) {
    // 过滤
    var filtered = words.where((entry) {
      final word = entry.key.toLowerCase();
      final details = entry.value;

      // 搜索过滤
      bool matchesSearch = true;
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        matchesSearch =
            word.contains(query) ||
            details.definition.toLowerCase().contains(query);
      }

      // 难度过滤
      bool matchesDifficulty =
          _filterDifficulty == 'all' || details.difficulty == _filterDifficulty;

      // 词性过滤
      bool matchesPartOfSpeech =
          _filterPartOfSpeech == 'all' ||
          details.partOfSpeech == _filterPartOfSpeech;

      return matchesSearch && matchesDifficulty && matchesPartOfSpeech;
    }).toList();

    // 排序
    filtered.sort((a, b) {
      int comparison = 0;

      switch (_sortBy) {
        case 'alphabetical':
          comparison = a.key.compareTo(b.key);
          break;
        case 'frequency':
          comparison = a.value.frequency.compareTo(b.value.frequency);
          break;
        case 'difficulty':
          final difficultyOrder = [
            'beginner',
            'intermediate',
            'advanced',
            'expert',
          ];
          final aIndex = difficultyOrder.indexOf(a.value.difficulty);
          final bIndex = difficultyOrder.indexOf(b.value.difficulty);
          comparison = aIndex.compareTo(bIndex);
          break;
        case 'length':
          comparison = a.key.length.compareTo(b.key.length);
          break;
      }

      return _sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  /// 显示排序选项
  void _showSortOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题栏
            Row(
              children: [
                const Expanded(
                  child: Text(
                    '排序选项',
                    style: TextStyle(
                      color: Color(0xFF37352F),
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close, color: Color(0xFF9B9A97)),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // 排序选项
            _buildSortOption('按字母顺序', 'alphabetical'),
            _buildSortOption('按词频', 'frequency'),
            _buildSortOption('按难度', 'difficulty'),
            _buildSortOption('按长度', 'length'),

            const SizedBox(height: 16),

            // 排序方向
            Row(
              children: [
                const Text(
                  '排序方向：',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF37352F),
                  ),
                ),
                const SizedBox(width: 16),
                ChoiceChip(
                  label: const Text('升序'),
                  selected: _sortAscending,
                  onSelected: (selected) {
                    setState(() {
                      _sortAscending = true;
                    });
                    Navigator.of(context).pop();
                  },
                ),
                const SizedBox(width: 8),
                ChoiceChip(
                  label: const Text('降序'),
                  selected: !_sortAscending,
                  onSelected: (selected) {
                    setState(() {
                      _sortAscending = false;
                    });
                    Navigator.of(context).pop();
                  },
                ),
              ],
            ),

            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  /// 构建排序选项
  Widget _buildSortOption(String title, String value) {
    final isSelected = _sortBy == value;

    return GestureDetector(
      onTap: () {
        setState(() {
          _sortBy = value;
        });
        Navigator.of(context).pop();
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFF2F76DA).withValues(alpha: 0.1)
              : const Color(0xFFF7F6F3),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? const Color(0xFF2F76DA)
                : const Color(0xFFE3E2E0),
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  color: isSelected
                      ? const Color(0xFF2F76DA)
                      : const Color(0xFF37352F),
                ),
              ),
            ),
            if (isSelected) ...[
              const Icon(Icons.check, color: Color(0xFF2F76DA), size: 20),
            ],
          ],
        ),
      ),
    );
  }

  /// 显示过滤选项
  void _showFilterOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题栏
            Row(
              children: [
                const Expanded(
                  child: Text(
                    '过滤选项',
                    style: TextStyle(
                      color: Color(0xFF37352F),
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close, color: Color(0xFF9B9A97)),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // 难度过滤
            const Text(
              '难度等级',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              children: [
                _buildFilterChip('全部', 'all', _filterDifficulty, (value) {
                  setState(() {
                    _filterDifficulty = value;
                  });
                }),
                _buildFilterChip('初级', 'beginner', _filterDifficulty, (value) {
                  setState(() {
                    _filterDifficulty = value;
                  });
                }),
                _buildFilterChip('中级', 'intermediate', _filterDifficulty, (
                  value,
                ) {
                  setState(() {
                    _filterDifficulty = value;
                  });
                }),
                _buildFilterChip('高级', 'advanced', _filterDifficulty, (value) {
                  setState(() {
                    _filterDifficulty = value;
                  });
                }),
                _buildFilterChip('专家', 'expert', _filterDifficulty, (value) {
                  setState(() {
                    _filterDifficulty = value;
                  });
                }),
              ],
            ),

            const SizedBox(height: 24),

            // 词性过滤
            const Text(
              '词性',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              children: [
                _buildFilterChip('全部', 'all', _filterPartOfSpeech, (value) {
                  setState(() {
                    _filterPartOfSpeech = value;
                  });
                }),
                _buildFilterChip('名词', 'noun', _filterPartOfSpeech, (value) {
                  setState(() {
                    _filterPartOfSpeech = value;
                  });
                }),
                _buildFilterChip('动词', 'verb', _filterPartOfSpeech, (value) {
                  setState(() {
                    _filterPartOfSpeech = value;
                  });
                }),
                _buildFilterChip('形容词', 'adjective', _filterPartOfSpeech, (
                  value,
                ) {
                  setState(() {
                    _filterPartOfSpeech = value;
                  });
                }),
                _buildFilterChip('副词', 'adverb', _filterPartOfSpeech, (value) {
                  setState(() {
                    _filterPartOfSpeech = value;
                  });
                }),
              ],
            ),

            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  /// 构建过滤芯片
  Widget _buildFilterChip(
    String label,
    String value,
    String currentValue,
    ValueChanged<String> onChanged,
  ) {
    final isSelected = value == currentValue;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        onChanged(value);
        Navigator.of(context).pop();
      },
      selectedColor: const Color(0xFF2F76DA).withValues(alpha: 0.2),
      checkmarkColor: const Color(0xFF2F76DA),
    );
  }
}

/// 单词列表项
class _WordListItem extends StatelessWidget {
  final String word;
  final WordDetails details;
  final bool isSelected;
  final bool isSelectionMode;
  final VoidCallback onTap;
  final ValueChanged<bool?> onSelectionChanged;

  const _WordListItem({
    required this.word,
    required this.details,
    required this.isSelected,
    required this.isSelectionMode,
    required this.onTap,
    required this.onSelectionChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: isSelected
            ? const Color(0xFF2F76DA).withValues(alpha: 0.05)
            : Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isSelected ? const Color(0xFF2F76DA) : const Color(0xFFE3E2E0),
        ),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: isSelectionMode
            ? Checkbox(
                value: isSelected,
                onChanged: (value) => onSelectionChanged(value ?? false),
                activeColor: const Color(0xFF2F76DA),
              )
            : Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: _getDifficultyColor(
                    details.difficulty,
                  ).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(
                    word.isNotEmpty ? word[0].toUpperCase() : '',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: _getDifficultyColor(details.difficulty),
                    ),
                  ),
                ),
              ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                word,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF37352F),
                ),
              ),
            ),
            if (details.phonetic != null) ...[
              Text(
                details.phonetic!,
                style: const TextStyle(
                  fontSize: 12,
                  color: Color(0xFF9B9A97),
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              details.definition,
              style: const TextStyle(fontSize: 14, color: Color(0xFF787774)),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                _buildTag(details.partOfSpeech, const Color(0xFF7C3AED)),
                const SizedBox(width: 8),
                _buildTag(
                  details.difficulty,
                  _getDifficultyColor(details.difficulty),
                ),
                const SizedBox(width: 8),
                if (details.frequency > 0)
                  _buildTag(
                    '频率: ${details.frequency}',
                    const Color(0xFF0F7B6C),
                  ),
              ],
            ),
          ],
        ),
        trailing: isSelectionMode
            ? null
            : const Icon(
                Icons.chevron_right,
                color: Color(0xFF9B9A97),
                size: 20,
              ),
        onTap: onTap,
      ),
    );
  }

  Widget _buildTag(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 10,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Color _getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return const Color(0xFF0F7B6C);
      case 'intermediate':
        return const Color(0xFFD9730D);
      case 'advanced':
        return const Color(0xFFE03E3E);
      case 'expert':
        return const Color(0xFF7C3AED);
      default:
        return const Color(0xFF787774);
    }
  }
}

/// 单词详情弹窗
class _WordDetailDialog extends StatelessWidget {
  final String word;
  final WordDetails details;
  final VoidCallback onAddToMemory;

  const _WordDetailDialog({
    required this.word,
    required this.details,
    required this.onAddToMemory,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: BoxConstraints(
          maxWidth: 500,
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题栏
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                border: Border(bottom: BorderSide(color: Color(0xFFE3E2E0))),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          word,
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF37352F),
                          ),
                        ),
                        if (details.phonetic != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            details.phonetic!,
                            style: const TextStyle(
                              fontSize: 16,
                              color: Color(0xFF9B9A97),
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Color(0xFF9B9A97)),
                  ),
                ],
              ),
            ),

            // 内容区域
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 基本信息标签
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        _buildInfoChip(
                          details.partOfSpeech,
                          const Color(0xFF7C3AED),
                        ),
                        _buildInfoChip(
                          details.difficulty,
                          _getDifficultyColor(details.difficulty),
                        ),
                        if (details.frequency > 0)
                          _buildInfoChip(
                            '频率: ${details.frequency}',
                            const Color(0xFF0F7B6C),
                          ),
                      ],
                    ),

                    const SizedBox(height: 20),

                    // 释义
                    _buildSection(
                      '释义',
                      Icons.description_outlined,
                      details.definition,
                    ),

                    // 例句
                    if (details.examples.isNotEmpty) ...[
                      const SizedBox(height: 20),
                      _buildSection(
                        '例句',
                        Icons.format_quote,
                        null,
                        children: [
                          for (final example in details.examples)
                            Container(
                              margin: const EdgeInsets.only(bottom: 8),
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: const Color(0xFFF7F6F3),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                example,
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Color(0xFF37352F),
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],

                    // 同义词
                    if (details.synonyms.isNotEmpty) ...[
                      const SizedBox(height: 20),
                      _buildSection(
                        '同义词',
                        Icons.compare_arrows,
                        details.synonyms.join(', '),
                      ),
                    ],

                    // 反义词
                    if (details.antonyms.isNotEmpty) ...[
                      const SizedBox(height: 20),
                      _buildSection(
                        '反义词',
                        Icons.swap_horiz,
                        details.antonyms.join(', '),
                      ),
                    ],

                    // 词源
                    if (details.etymology != null) ...[
                      const SizedBox(height: 20),
                      _buildSection(
                        '词源',
                        Icons.history_edu,
                        details.etymology!,
                      ),
                    ],
                  ],
                ),
              ),
            ),

            // 底部操作栏
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                border: Border(top: BorderSide(color: Color(0xFFE3E2E0))),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        side: const BorderSide(color: Color(0xFFE3E2E0)),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        '关闭',
                        style: TextStyle(
                          color: Color(0xFF787774),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        onAddToMemory();
                        Navigator.of(context).pop();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF2F76DA),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        '加入记忆',
                        style: TextStyle(fontWeight: FontWeight.w600),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建信息标签
  Widget _buildInfoChip(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 构建内容区块
  Widget _buildSection(
    String title,
    IconData icon,
    String? content, {
    List<Widget>? children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 18, color: const Color(0xFF2F76DA)),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        if (content != null)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFFF7F6F3),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: const Color(0xFFE3E2E0)),
            ),
            child: Text(
              content,
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF37352F),
                height: 1.5,
              ),
            ),
          ),
        if (children != null) ...children,
      ],
    );
  }

  /// 获取难度颜色
  Color _getDifficultyColor(String difficulty) {
    switch (difficulty.toLowerCase()) {
      case 'beginner':
        return const Color(0xFF0F7B6C);
      case 'intermediate':
        return const Color(0xFFD9730D);
      case 'advanced':
        return const Color(0xFFE03E3E);
      case 'expert':
        return const Color(0xFF7C3AED);
      default:
        return const Color(0xFF787774);
    }
  }
}
